import asyncio
import getpass
import json
import os

from dotenv import load_dotenv
from IPython.display import Image, display
from langchain.chat_models import init_chat_model
from langchain_core.messages import AIMessageChunk, HumanMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph_supervisor import create_supervisor

from states import AgentState, AgentStateInput
from tasks import extract_scope_specifications
from tasks.report_planner import report_planner_builder
from tasks.specifications_enhancer import specifications_enhancer_builder

load_dotenv()  # take environment variables

if not os.environ.get("GOOGLE_API_KEY"):
    os.environ["GOOGLE_API_KEY"] = getpass.getpass("Enter API key for Google Gemini: ")
if not os.environ.get("OPENAI_API_KEY"):
    os.environ["OPENAI_API_KEY"] = getpass.getpass("Enter API key for OpenAI: ")
if not os.environ.get("TAVILY_API_KEY"):
    os.environ["TAVILY_API_KEY"] = getpass.getpass("Tavily API key:\n")

api_key = os.getenv("GOOGLE_API_KEY")
tavily_api_key = os.getenv("TAVILY_API_KEY")
openai_api_key = os.getenv("OPENAI_API_KEY")


def serialise_ai_message_chunk(chunk):
    if isinstance(chunk, AIMessageChunk):
        return chunk.content
    else:
        raise TypeError(
            f"Object of type {type(chunk).__name__} is not correctly formatted for serialisation"
        )


def create_deep_research_workflow(memory):
    # Supervisor agent
    supervisor = create_supervisor(
        model=init_chat_model("gemini-2.0-flash", model_provider="google_genai"),
        agents=[
            specifications_enhancer_builder.compile(name="specifications_enhancer"),
            report_planner_builder.compile(name="report_planner"),
        ],
        prompt=(
            "You are a supervisor managing two agents:\n"
            "- a specifications enhancer agent. Assign specifications enhancement tasks to this agent\n"
            "- a report planner agent. Assign report planning, web search, and report writing tasks to this agent\n"
            "Assign work to one agent at a time, do not call agents in parallel.\n"
            "Do not do any work yourself.\n"
            "You will receieve a scope specification from the user. Always try to improve the scope specification before assigning work to the report planner."
        ),
        add_handoff_back_messages=True,
        output_mode="full_history",
        state_schema=AgentState,
    ).compile(name="supervisor")

    graph_builder = StateGraph(AgentState, input=AgentStateInput)
    graph_builder.add_node("extract_scope_specifications", extract_scope_specifications)
    graph_builder.add_node("supervisor", supervisor)
    graph_builder.add_edge(START, "extract_scope_specifications")
    graph_builder.add_edge("extract_scope_specifications", "supervisor")

    display(Image(supervisor.get_graph().draw_mermaid_png()))

    return graph_builder.compile(checkpointer=memory)


# events = graph.astream(input=state, config=config, stream_mode="updates")
# # graph.invoke(state, config=config)

# for chunk in events:
#     for node_id, value in chunk.items():
#             #  If we reach an interrupt, continuously ask for human feedback
#             print(f"[{node_id}]: {value}")
#             print("=================================================================")
#             if(node_id == "__interrupt__"):
#                 while True:
#                     user_feedback = input("Please answer the questions to the best of your ability (or type 'done' to exit): ")

#                     # Resume the graph execution with the user's feedback
#                     graph.invoke(Command(resume=user_feedback), config=config)

#                     # Exit loop if user says done
#                     if user_feedback.lower() == "done":
#                         break


async def run_async_workflow(topic: str):
    # Initialize memory saver for checkpointing
    memory = MemorySaver()

    """Run the async workflow with human feedback loop"""
    app = create_deep_research_workflow(memory)

    topic = "SECTION II\n\nSCOPE OF REQUIREMENTS\n\nS/N\nTITLE\n\nSCOPE OF REQUIREMENTS\n1\nSCOPE \n:\nDEVELOPMENT OF A CUSTOMIZED ROUTE DELIVERY ROBOT\n\n\n\n\n2\nSPECIFICATIONS:\n2.1\tCustomized Route Delivery Robot\n\n\n\nGENERAL\n\n            2.1.1 The Contractor shall provide one (1) fully automated and customized route intelligent delivery service robot designed for deployment within an office environment.\n\n            2.1.2 The System shall have features of autonomous navigation mode using VSLAM (Visual Simultaneous Localization and Mapping) and shall be controllable via a mobile application (App-Controlled) or remote controls.\n\n            2.1.3 The System shall incorporate safety features, including obstacle avoidance, to ensure user protection from potential collisions.\n\n            2.1.4 The System shall offer a runtime of 24 hours on a single charge of 8 hours, with an auto-charge function enabling autonomous recharging during office hours and continued autonomous operation beyond office hours.\n\n            2.1.5 The System shall be equipped with a 10-inch operating screen to display robot’s status and enable user to toggle between various robot functions selection.\n\n            2.1.6 The System shall not exceed a footprint of 38 x 38 x 103cm, allowing for easy maneuverability within the office space.\n\n            2.1.7 The System shall be accompanied by a 1-year warranty and shall provide after-warranty service, including at least video technical support.\n\n\n\n\n\t\n"

    # Create initial state
    initial_state = {
        "topic": topic,
        "messages": [
            HumanMessage(
                content="Enhance the specifications from user, and write up a procurement report through research"
            )
        ],
    }

    # Configuration for the run
    config: RunnableConfig = {"configurable": {"thread_id": "conversation-1"}}

    print(f"🚀 Starting deep research workflow")
    print("=" * 80)

    try:
        # Stream the workflow execution
        async for event in app.astream_events(
            input=initial_state,
            config=config,
            version="v2",
            stream_mode="messages",
            subgraphs=True,
        ):
            data = event["data"]
            name = event["name"]
            event_type = event["event"]
            data_output = data.get("output")

            # For human input interrupts
            # if(node_id == "__interrupt__"):
            #     while True:
            #         user_feedback = input("Provide feedback (or type 'done' when finished): ")

            #         # Resume the graph execution with the user's feedback
            #         app.invoke(Command(resume=user_feedback), config=thread_config)

            #         # Exit loop if user says done
            #         if user_feedback.lower() == "done":
            #             break

            if event_type == "on_chat_model_stream":
                chunk_content = serialise_ai_message_chunk(data.get("chunk"))
                # Escape single quotes and newlines for safe JSON parsing
                if type(chunk_content) == str:
                    chunk_content = chunk_content.replace("'", "\\'").replace(
                        "\n", "\\n"
                    )

                print(chunk_content, end="", flush=True)
            else:
                print(f"[${name}]: {data}", end="\n\n", flush=True)

        #         # print("data: {{\"type\": \"content\", \"content\": \"{safe_content}\"}}\n\n")
        #         # yield f"data: {{\"type\": \"content\", \"content\": \"{safe_content}\"}}\n\n"

        #     elif event_type == "on_chat_model_end":
        #         # Check if there are tool calls for search
        #         tool_calls = []
        #         if data_output and hasattr(data_output, "tool_calls"):
        #             tool_calls = data_output.tool_calls

        #         search_calls = [call for call in tool_calls if call["name"] == "tavily_search_results_json"]

        #         if search_calls:
        #             # Signal that a search is starting
        #             search_query = search_calls[0]["args"].get("query", "")
        #             # Escape quotes and special characters
        #             safe_query = search_query.replace('"', '\\"').replace("'", "\\'").replace("\n", "\\n")

        #             print(f"data: {{\"type\": \"search_start\", \"query\": \"{safe_query}\"}}\n\n")
        #             # yield f"data: {{\"type\": \"search_start\", \"query\": \"{safe_query}\"}}\n\n"

        #     elif event_type == "on_tool_end" and event["name"] == "tavily_search_results_json":
        #         # Search completed - send results or error
        #         output = data.get("output")

        #         # Check if output is a list
        #         if isinstance(output, list):
        #             # Extract URLs from list of search results
        #             urls = []
        #             for item in output:
        #                 if isinstance(item, dict) and "url" in item:
        #                     urls.append(item["url"])

        #             # Convert URLs to JSON and yield them
        #             urls_json = json.dumps(urls)

        #             print(f"data: {{\"type\": \"search_results\", \"urls\": {urls_json}}}\n\n")
        #             # yield f"data: {{\"type\": \"search_results\", \"urls\": {urls_json}}}\n\n"

        # # Send an end event
        # print(f"data: {{\"type\": \"end\"}}\n\n")
        # yield f"data: {{\"type\": \"end\"}}\n\n"

        print("\n✅ Workflow completed!")

    except Exception as e:
        print(f"❌ Error during workflow execution: {e}")


async def main():
    """Main function to demonstrate the workflow"""
    topic = "SECTION II\n\nSCOPE OF REQUIREMENTS\n\nS/N\nTITLE\n\nSCOPE OF REQUIREMENTS\n1\nSCOPE \n:\nDEVELOPMENT OF A CUSTOMIZED ROUTE DELIVERY ROBOT\n\n\n\n\n2\nSPECIFICATIONS:\n2.1\tCustomized Route Delivery Robot\n\n\n\nGENERAL\n\n            2.1.1 The Contractor shall provide one (1) fully automated and customized route intelligent delivery service robot designed for deployment within an office environment.\n\n            2.1.2 The System shall have features of autonomous navigation mode using VSLAM (Visual Simultaneous Localization and Mapping) and shall be controllable via a mobile application (App-Controlled) or remote controls.\n\n            2.1.3 The System shall incorporate safety features, including obstacle avoidance, to ensure user protection from potential collisions.\n\n            2.1.4 The System shall offer a runtime of 24 hours on a single charge of 8 hours, with an auto-charge function enabling autonomous recharging during office hours and continued autonomous operation beyond office hours.\n\n            2.1.5 The System shall be equipped with a 10-inch operating screen to display robot’s status and enable user to toggle between various robot functions selection.\n\n            2.1.6 The System shall not exceed a footprint of 38 x 38 x 103cm, allowing for easy maneuverability within the office space.\n\n            2.1.7 The System shall be accompanied by a 1-year warranty and shall provide after-warranty service, including at least video technical support.\n\n\n\n\n\t\n"

    await run_async_workflow(topic)


if __name__ == "__main__":
    asyncio.run(main())
