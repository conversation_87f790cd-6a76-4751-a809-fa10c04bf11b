[project]
name = "deep_research_agent"
version = "1.0.0"
description = "Refinement, Planning, research, and report generation."
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.9"
dependencies = [
    "langgraph",
    "langsmith",
    "google-genai",
    "langchain_google_genai",
    "langchain_tavily",
    "langchain",
    "python-dotenv",
    "grandalf",
    "arxiv>=2.1.3",
    "exa-py>=1.8.9",
    "langchain-community",
    "langchain-anthropic",
    "langchain-openai",
    "requests",
    "tabulate",
    "pandas",
    "numpy",
    "xmltodict",
    "pymupdf>=1.25.3",
    "openai",
    "tavily-python",
    "langchain-groq",
    "linkup-sdk",
    "IPython",
    "langgraph-supervisor",
    "apify_client",
    "datamodel-code-generator",
    "pre-commit",
    "black",
    "isort",
    "flake8"
]


[tool.setuptools]
packages = ["deep_research_agent"]

[tool.setuptools.package-dir]
"deep_research_agent" = "./"

[tool.setuptools.package-data]
"*" = ["py.typed"]


[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    ".venv",
    "migrations"
]