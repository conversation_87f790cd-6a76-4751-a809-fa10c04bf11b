stages:
  - format
  - test
  - build

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"

cache:
  paths:
    - .cache/pip/

auto-format:
  stage: format
  image: python:3.11-slim
  before_script:
    - pip install --upgrade pip
    - pip install black isort #flake8
  script:
    - echo "Running auto-formatting..."
    - black . --check --diff || (black . && echo "Code formatted")
    - isort . --check-only --diff || (isort . && echo "Imports sorted")
    - |
      if [[ `git status --porcelain` ]]; then
        echo "Code was auto-formatted, committing changes..."
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitLab CI Auto-Formatter"
        git add .
        git commit -m "🤖 Auto-format code [skip ci]"
        git push https://gitlab-ci-token:${CI_PUSH_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git HEAD:${CI_COMMIT_REF_NAME}
      else
        echo "No formatting changes needed"
      fi
  only:
    - merge_requests
    - main
  except:
    variables:
      - $CI_COMMIT_MESSAGE =~ /\[skip ci\]/

test:
  stage: test
  image: python:3.11-slim
  script:
    - pip install -r requirements.txt
    #- flake8 . --count --show-source --statistics
    - echo "Add your actual tests here"
  dependencies:
    - auto-format