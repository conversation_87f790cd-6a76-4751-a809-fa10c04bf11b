import asyncio
import j<PERSON>
from typing import Dict, List, Optional

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from pydantic import BaseModel

from main import create_deep_research_workflow, serialise_ai_message_chunk


class ResearchRequest(BaseModel):
    topic: str
    thread_id: Optional[str] = "default-thread"


class ResearchResponse(BaseModel):
    status: str
    message: str
    thread_id: str


class StreamEvent(BaseModel):
    type: str
    content: Optional[str] = None
    data: Optional[Dict] = None


app = FastAPI(
    title="Deep Research Agent API",
    description="API for the Deep Research Agent workflow",
    version="1.0.0",
)

# Global memory instance
memory = MemorySaver()


@app.get("/")
async def root():
    return {"message": "Deep Research Agent API is running"}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


@app.post("/research", response_model=ResearchResponse)
async def start_research(request: ResearchRequest):
    """Start a research workflow (non-streaming)"""
    try:
        # Create the workflow
        workflow = create_deep_research_workflow(memory)
        
        # Create initial state
        initial_state = {
            "topic": request.topic,
            "messages": [
                HumanMessage(
                    content="Enhance the specifications from user, and write up a procurement report through research"
                )
            ],
        }

        # Configuration for the run
        config: RunnableConfig = {"configurable": {"thread_id": request.thread_id}}

        # Run the workflow
        result = await workflow.ainvoke(initial_state, config=config)
        
        return ResearchResponse(
            status="completed",
            message="Research workflow completed successfully",
            thread_id=request.thread_id
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Workflow execution failed: {str(e)}")


@app.post("/research/stream")
async def stream_research(request: ResearchRequest):
    """Start a research workflow with streaming responses"""
    
    async def generate_stream():
        try:
            # Create the workflow
            workflow = create_deep_research_workflow(memory)
            
            # Create initial state
            initial_state = {
                "topic": request.topic,
                "messages": [
                    HumanMessage(
                        content="Enhance the specifications from user, and write up a procurement report through research"
                    )
                ],
            }

            # Configuration for the run
            config: RunnableConfig = {"configurable": {"thread_id": request.thread_id}}

            # Stream the workflow execution
            async for event in workflow.astream_events(
                input=initial_state,
                config=config,
                version="v2",
                stream_mode="messages",
                subgraphs=True,
            ):
                data = event["data"]
                name = event["name"]
                event_type = event["event"]
                
                if event_type == "on_chat_model_stream":
                    chunk_content = serialise_ai_message_chunk(data.get("chunk"))
                    if isinstance(chunk_content, str) and chunk_content.strip():
                        stream_event = StreamEvent(
                            type="content",
                            content=chunk_content
                        )
                        yield f"data: {stream_event.model_dump_json()}\n\n"
                
                elif event_type == "on_chat_model_start":
                    stream_event = StreamEvent(
                        type="agent_start",
                        data={"agent": name}
                    )
                    yield f"data: {stream_event.model_dump_json()}\n\n"
                
                elif event_type == "on_chat_model_end":
                    stream_event = StreamEvent(
                        type="agent_end",
                        data={"agent": name}
                    )
                    yield f"data: {stream_event.model_dump_json()}\n\n"
                
                elif event_type == "on_tool_start":
                    stream_event = StreamEvent(
                        type="tool_start",
                        data={"tool": name, "input": data.get("input")}
                    )
                    yield f"data: {stream_event.model_dump_json()}\n\n"
                
                elif event_type == "on_tool_end":
                    # For search results, extract URLs
                    if name == "tavily_search_results_json":
                        output = data.get("output")
                        urls = []
                        if isinstance(output, list):
                            for item in output:
                                if isinstance(item, dict) and "url" in item:
                                    urls.append(item["url"])
                        
                        stream_event = StreamEvent(
                            type="search_results",
                            data={"urls": urls}
                        )
                        yield f"data: {stream_event.model_dump_json()}\n\n"
                    else:
                        stream_event = StreamEvent(
                            type="tool_end",
                            data={"tool": name}
                        )
                        yield f"data: {stream_event.model_dump_json()}\n\n"

            # Send completion event
            completion_event = StreamEvent(
                type="completed",
                data={"thread_id": request.thread_id}
            )
            yield f"data: {completion_event.model_dump_json()}\n\n"
            
        except Exception as e:
            error_event = StreamEvent(
                type="error",
                data={"error": str(e)}
            )
            yield f"data: {error_event.model_dump_json()}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
        }
    )


@app.get("/threads")
async def list_threads():
    """List all active threads (conversations)"""
    # Note: MemorySaver doesn't have a built-in method to list threads
    # This is a placeholder - you might want to implement thread tracking
    return {"message": "Thread listing not implemented with MemorySaver"}


@app.get("/threads/{thread_id}/history")
async def get_thread_history(thread_id: str):
    """Get conversation history for a specific thread"""
    try:
        config: RunnableConfig = {"configurable": {"thread_id": thread_id}}
        
        # Get the current state for this thread
        workflow = create_deep_research_workflow(memory)
        state = await workflow.aget_state(config)
        
        if state.values:
            return {
                "thread_id": thread_id,
                "messages": [msg.dict() if hasattr(msg, 'dict') else str(msg) 
                           for msg in state.values.get("messages", [])],
                "topic": state.values.get("topic", ""),
            }
        else:
            return {"thread_id": thread_id, "messages": [], "topic": ""}
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get thread history: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)