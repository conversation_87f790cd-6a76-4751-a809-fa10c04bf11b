# Setup
```
python -m venv .venv
source ./.venv/bin/activate
.venv\Scripts\Activate.ps1 # Windows
```

## Using poetry
Adding new packages - It will automatically find a suitable version constraint and install the package and sub-dependencies.
```
poetry add {package_name}
```

Install dependencies
```
poetry install
```

Running the app
```
poetry run python your_script.py
```

1. Generate models (run this when contracts change):
```bash
datamodel-codegen --input api-contracts/user.yaml --output src/models/user.py --input-file-type openapi
```

Launch the assistant with the LangGraph server locally, which will open in your browser:

```bash
# Install uv package manager
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies and start the LangGraph server
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.11 langgraph dev --allow-blocking
```

## Errors
```
Heads up! LangGraph dev identified a synchronous blocking call in your code. When running in an ASGI web server, blocking calls can degrade performance for everyone since they tie up the event loop.

Here are your options to fix this:

1. Best approach: Convert any blocking code to use async/await patterns
   For example, use 'await aiohttp.get()' instead of 'requests.get()'

2. Quick fix: Move blocking operations to a separate thread
   Example: 'await asyncio.to_thread(your_blocking_function)'

3. Override (if you can't change the code):
   - For development: Run 'langgraph dev --allow-blocking'
   - For deployment: Set 'BG_JOB_ISOLATED_LOOPS=true' environment variable
```

# Check for pre-commits
```bash
# In ford-api-ai directory
pip install -r requirements.txt
pre-commit install
pre-commit run --all-files  # Test formatting on existing code
```