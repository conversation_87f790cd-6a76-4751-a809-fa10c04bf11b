import asyncio

from lang<PERSON> import traceable
from tavily import AsyncTavilyClient


@traceable
async def tavily_search_async(search_queries, number_sources_per_query=3):
    """
    Performs concurrent web searches using the Tavily API.

    Args:
        search_queries (List[SearchQuery]): List of search queries to process

    Returns:
            List[dict]: List of search responses from Tavily API, one per query. Each response has format:
                {
                    'query': str, # The original search query
                    'follow_up_questions': None,
                    'answer': None,
                    'images': list,
                    'results': [                     # List of search results
                        {
                            'title': str,            # Title of the webpage
                            'url': str,              # URL of the result
                            'content': str,          # Summary/snippet of content
                            'score': float,          # Relevance score
                            'raw_content': str|None  # Full page content if available
                        },
                        ...
                    ]
                }
    """
    tavily_async_client = AsyncTavilyClient()
    search_tasks = []
    for query in search_queries:
        search_tasks.append(
            tavily_async_client.search(
                query,
                max_results=number_sources_per_query,
                search_depth="advanced",
                include_raw_content=True,
                include_domains=["arxiv.org"],
            )
        )

    # Execute all searches concurrently
    search_docs = await asyncio.gather(*search_tasks)
    # get only 50.000 characters of raw content
    # for search_doc in search_docs:
    #     for result in search_doc["results"]:
    #         if result["raw_content"]:
    #             result["raw_content"] = result["raw_content"][:50000]
    return search_docs
