import asyncio

from langchain_community.retrievers import ArxivRetriever
from langsmith import traceable


@traceable
async def arxiv_search_async(
    search_queries,
    load_max_docs=5,
    number_sources_per_query=3,
    get_full_documents=True,
    load_all_available_meta=True,
):
    """
    Performs concurrent searches on arXiv using the ArxivRetriever.

    Args:
        search_queries (List[str]): List of search queries or article IDs
        load_max_docs (int, optional): Maximum number of documents to return per query. Default is 5.
        get_full_documents (bool, optional): Whether to fetch full text of documents. Default is True.
        load_all_available_meta (bool, optional): Whether to load all available metadata. Default is True.

    Returns:
        List[dict]: List of search responses from arXiv, one per query. Each response has format:
            {
                'query': str,                    # The original search query
                'follow_up_questions': None,
                'answer': None,
                'images': [],
                'results': [                     # List of search results
                    {
                        'title': str,            # Title of the paper
                        'url': str,              # URL (Entry ID) of the paper
                        'content': str,          # Formatted summary with metadata
                        'score': float,          # Relevance score (approximated)
                        'raw_content': str|None  # Full paper content if available
                        'summarzation_raw_content': str|None  # Summarization of the raw content
                    },
                    ...
                ]
            }
    """

    async def process_single_query(query):
        try:
            # Create retriever for each query
            retriever = ArxivRetriever(
                load_max_docs=load_max_docs,
                top_k_results=number_sources_per_query,
                get_full_documents=get_full_documents,
                load_all_available_meta=load_all_available_meta,
                doc_content_chars_max=50000,
            )

            # Run the synchronous retriever in a thread pool
            loop = asyncio.get_event_loop()
            docs = await loop.run_in_executor(None, lambda: retriever.invoke(query))

            results = []
            # Assign decreasing scores based on the order
            base_score = 1.0
            score_decrement = 1.0 / (len(docs) + 1) if docs else 0

            for i, doc in enumerate(docs):
                # Extract metadata
                metadata = doc.metadata

                # Use entry_id as the URL (this is the actual arxiv link)
                url = metadata.get("entry_id", "")

                # Format content with all useful metadata
                content_parts = []

                # Primary information
                if "Summary" in metadata:
                    content_parts.append(f"Summary: {metadata['Summary']}")

                if "Authors" in metadata:
                    content_parts.append(f"Authors: <AUTHORS>

                # Add publication information
                published = metadata.get("Published")
                published_str = (
                    published.isoformat()
                    if hasattr(published, "isoformat")
                    else str(published)
                    if published
                    else ""
                )
                if published_str:
                    content_parts.append(f"Published: {published_str}")

                # Add additional metadata if available
                if "primary_category" in metadata:
                    content_parts.append(
                        f"Primary Category: {metadata['primary_category']}"
                    )

                if "categories" in metadata and metadata["categories"]:
                    content_parts.append(
                        f"Categories: {', '.join(metadata['categories'])}"
                    )

                if "comment" in metadata and metadata["comment"]:
                    content_parts.append(f"Comment: {metadata['comment']}")

                if "journal_ref" in metadata and metadata["journal_ref"]:
                    content_parts.append(
                        f"Journal Reference: {metadata['journal_ref']}"
                    )

                if "doi" in metadata and metadata["doi"]:
                    content_parts.append(f"DOI: {metadata['doi']}")

                # Get PDF link if available in the links
                pdf_link = ""
                if "links" in metadata and metadata["links"]:
                    for link in metadata["links"]:
                        if "pdf" in link:
                            pdf_link = link
                            content_parts.append(f"PDF: {pdf_link}")
                            break

                # Join all content parts with newlines
                content = "\n".join(content_parts)

                result = {
                    "title": metadata.get("Title", ""),
                    "url": url,  # Using entry_id as the URL
                    "content": content,
                    "score": base_score - (i * score_decrement),
                    "raw_content": (doc.page_content if get_full_documents else None),
                    "summarization_raw_content": None,
                }
                results.append(result)

            return {
                "query": query,
                "follow_up_questions": None,
                "answer": None,
                "images": [],
                "results": results,
            }
        except Exception as e:
            # Handle exceptions gracefully
            print(f"Error processing arXiv query '{query}': {str(e)}")
            return {
                "query": query,
                "follow_up_questions": None,
                "answer": None,
                "images": [],
                "results": [],
                "error": str(e),
            }

    # Create a rate limiter using asyncio.Semaphore
    # Allow only one request at a time to respect ArXiv's rate limit
    rate_limiter = asyncio.Semaphore(1)

    async def rate_limited_query(query, index):
        async with rate_limiter:
            # Add delay between requests (3 seconds per ArXiv's rate limit)
            if index > 0:  # Don't delay the first request
                await asyncio.sleep(3.0)
            return await process_single_query(query)

    # Create tasks for all queries with rate limiting
    tasks = [rate_limited_query(query, i) for i, query in enumerate(search_queries)]

    # Execute all tasks and collect results while preserving order
    search_docs = await asyncio.gather(*tasks)

    return search_docs
