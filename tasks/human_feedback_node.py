from typing import List, Literal

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.graph import END, START, StateGraph
from langgraph.types import Command, interrupt
from pydantic import BaseModel, Field

from states import AgentState


def human_feedback(state: AgentState):
    """Human Intervention node - loops back to enhancer unless input is done"""

    print("\n [human_node] awaiting human feedback...")

    specification_refinement_questionss = state["specification_refinement_questionss"]

    # Interrupt to get user feedback
    user_feedback = interrupt(
        {
            "questions": specification_refinement_questionss,
            "message": "Provide answer these questions if you can. Otherwise, type 'done' to finalise.",
        }
    )

    print(f"[human_node] Received human feedback: {user_feedback}")

    # If user types "done", transition to END node
    if user_feedback.lower() == "done":
        return Command(
            update={"refinement_feedback": user_feedback}, goto="replan_specifications"
        )

    # Otherwise, update feedback and return to model for re-generation
    return Command(
        update={"refinement_feedback": user_feedback}, goto="replan_specifications"
    )
