import asyncio
from typing import List

import pdfplumber
from langchain.chat_models import init_chat_model
from langchain.schema import Document
from langchain_community.vectorstores import Chroma
from langchain_core.messages import (
    AIMessage,
    AIMessageChunk,
    HumanMessage,
    SystemMessage,
)
from langchain_openai import OpenAIEmbeddings

# from tasks.vendor_evaluator.vendor_comparison_node import vendor_comparison
# from tasks.vendor_evaluator.states import VendorEvaluatorState
from langgraph.graph import END, START, StateGraph
from pydantic import BaseModel, Field


def create_markdown_file(filename, content):
    """
    Create a markdown file with the given content.

    Args:
        filename (str): Name of the markdown file (should end with .md)
        content (str): Markdown content to write
    """
    try:
        with open(filename, "w", encoding="utf-8") as file:
            file.write(content)
        print(f"Markdown file '{filename}' created successfully!")
    except Exception as e:
        print(f"Error creating markdown file: {str(e)}")


def extract_pdf_text(filename: str):
    """
    Extract text content from a PDF file.

    Args:
        filename (str): Path to the PDF file
        method (str): 'pypdf2' or 'pdfplumber' - extraction method to use

    Returns:
        str: Extracted text content from all pages
    """
    try:
        with pdfplumber.open(filename) as pdf:
            text = ""
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
            return text.strip()

    except FileNotFoundError:
        return f"Error: File '{filename}' not found."
    except Exception as e:
        return f"Error reading PDF: {str(e)}"


class VendorEvaluatorState(BaseModel):
    vendor_proposals: List[str] = Field(
        description="List of filenames of companies from the vendor proposals"
    )
    price_assessment_document: List[str]
    final_report: str


async def vendor_comparison(state: VendorEvaluatorState):
    """
    Can even hook up MCP server to access private database
    """
    print(state)
    vendor_proposals = state.vendor_proposals
    price_assessment_document = state.price_assessment_document

    # Extract text from documents
    vendor_proposals_text = ""
    for vendor_proposal in vendor_proposals:
        # TODO: look up database to get the actual contents of the file
        content = extract_pdf_text(vendor_proposal)
        vendor_proposals_text += content + "\n\n"

    price_assessment_document_text = ""
    for document in price_assessment_document:
        content = extract_pdf_text(document)
        price_assessment_document_text += content + "\n\n"

    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")

    vendor_comparison_instructions = """ 
        Given these

        Price-Assesment documents: {price_assessment_document}
        Vendor proposal documents: {vendor_proposals}

        You are an expert at evaluating bids and proposals from different vendors tasked with rewriting the following report in a formal and academic writing style.
        Ensure clarity, precision, and coherence throughout the text. 
        Rewrite citations using sequential numbering from 1 to number_of_sources (e.g., [1](link), [2](link), etc.)
        Consolidate all sources into a single reference section at the end of the report, rather than listing them separately at the end of each chapter.
        Given this tender (taken from the following documents)

        <tender>
        {price_assessment_document}
        <tender>

        Your job is to evaluate the different vendor proposals from the following documents:

        <vendors>
        {vendor_proposals}
        <vendors>

        Context is that these vendors will be bidding for the tender, we will evaluate and give the tender to the best suitable vendor based on their proposal.
        There are some important factors to access:
        1. Scope - Does the proposal fulfil the requirements stated in the tender
        2. Price - Does the budget align close with what we have proposed or estimated?
        3. Time - Is the time required too long or well within the duration we have estimated?

        For each vendor assessment, analyze it completely and provide reasons why one is better than the other.
        Specifications found in tender is an absolute must.
        Draw up a table to analyse and compare missing requirements and specifications.
        If vendor proposal doesnt contain any requirements or plans on a particular specifications, default it to "N/A".

        For every finding, you will to cite it with a snippet of the evidence in the appendix page.
        Give a rating upon 100 at the end (100 being most suitable vendor, 0 being out-of-scope)

        Generate a markdown report. Do not include your thoughts, only output the final report.

        <Quality Checks>
        - For introduction: 100-150 word limit, # for report title, no structural elements, no sources section
        - For conclusion: 100-150 word limit, ## for section title, only ONE structural element at most, no sources section
        - Markdown format
        - Do not include word count or any preamble in your response
        </Quality Checks>
    """

    vendor_comparison_prompt = vendor_comparison_instructions.format(
        price_assessment_document=price_assessment_document_text,
        vendor_proposals=vendor_proposals_text,
    )

    planner_message = (
        """Generate a summary report on which vendor best suits the tender"""
    )

    final_report = await llm.ainvoke(
        [
            SystemMessage(content=vendor_comparison_prompt),
            HumanMessage(content=planner_message),
        ]
    )

    return {"final_report": final_report.content}


async def generate_markdown_final_report(state: VendorEvaluatorState):
    create_markdown_file("final_report.md", state.final_report)
    return state


async def main():
    vendor_evaluator_builder = StateGraph(VendorEvaluatorState)
    vendor_evaluator_builder.add_node("vendor_comparison", vendor_comparison)
    vendor_evaluator_builder.add_node(
        "generate_markdown_final_report", generate_markdown_final_report
    )

    vendor_evaluator_builder.add_edge(START, "vendor_comparison")
    vendor_evaluator_builder.add_edge(
        "vendor_comparison", "generate_markdown_final_report"
    )
    vendor_evaluator_builder.add_edge("generate_markdown_final_report", END)

    vendor_evaluator_graph = vendor_evaluator_builder.compile()

    input = {
        "vendor_proposals": ["nextbot.pdf", "swiftbot.pdf"],
        "price_assessment_document": ["hardware_search.md", "report.md"],
        "final_report": "",
    }

    async for event in vendor_evaluator_graph.astream_events(input=input):
        data = event["data"]
        name = event["name"]
        event_type = event["event"]
        data_output = data.get("output")
        # print(f"[${name}]: {data_output}", end="\n\n", flush=True)
        # for chunk in data_output:
        #     if(isinstance(chunk, AIMessageChunk)):


if __name__ == "__main__":
    asyncio.run(main())
