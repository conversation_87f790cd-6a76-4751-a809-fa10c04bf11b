from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.graph import START, StateGraph
from pydantic import BaseModel, Field

from tasks.vendor_evaluator.states import VendorEvaluatorState


async def vendor_comparison(state: VendorEvaluatorState):
    """
    Can even hook up MCP server to access private database
    """
    vendor_proposals = state["vendor_proposals"]
    price_assessment_document = state["price_assessment_document"]

    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")

    vendor_comparison_instructions = """ 
        Given these

        Price-Assesment documents: {price_assessment_document}
        Vendor proposal documents: {vendor_proposals}

        You are an expert at evaluating bids and proposals from different vendors.
        Given this tender (taken from the following documents)

        <tender>
        {price_assessment_document}
        <tender>

        Your job is to evaluate the different vendor proposals from the following documents:

        <vendors>
        {vendor_proposals}
        <vendors>

        Context is that these vendors will be bidding for the tender, we will evaluate and give the tender to the best suitable vendor based on their proposal.
        There are some important factors to access:
        1. <PERSON>ope - Does the proposal fulfil the requirements stated in the tender
        2. Price - Does the budget align close with what we have proposed or estimated?
        3. Time - Is the time required too long or well within the duration we have estimated?

        For each vendor assessment, analyze it completely and provide reasons why one is better than the other.
        Specifications found in tender is an absolute must.
        Draw up a table to analyse and compare missing requirements and specifications.
        If vendor proposal doesnt contain any requirements or plans on a particular specifications, default it to "N/A".

        For every finding, you will to cite it with a snippet of the evidence in the appendix page.
        Give a rating upon 100 at the end (100 being most suitable vendor, 0 being out-of-scope)
    """

    vendor_comparison_prompt = vendor_comparison_instructions.format(
        price_assessment_document=price_assessment_document,
        vendor_proposals=vendor_proposals,
    )

    planner_message = (
        """Generate a summary report on which vendor best suits the tender"""
    )

    final_report = await llm.ainvoke(
        [
            SystemMessage(content=vendor_comparison_prompt),
            HumanMessage(content=planner_message),
        ]
    )

    return {"final_report": final_report}
