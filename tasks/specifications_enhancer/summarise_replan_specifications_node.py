from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage

from tasks.specifications_enhancer.states import EnhancerState, ScopeSpecifications

summarise_replan_specifications_prompt = """
Given the old specifications and the new research specifications:

<old_specifications>
{old_specifications}
<old_specifications>

<new_research_specifications>
{new_research_specifications}
<new_research_specifications>

Add new specifications to the old specifications.
Update old specifications if needed but be sure to keep the details of the old specifications with zero to minimal changes. 

Present it nicely in point form.
"""


def summarise_replan_specifications(state: EnhancerState):
    scope_specifications = state["scope_specifications"]
    scope = scope_specifications.scope
    specifications = scope_specifications.specifications
    new_research_specifications = state["final_answer"]

    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    specifications_comparison_instructions = (
        summarise_replan_specifications_prompt.format(
            old_specifications=specifications,
            new_research_specifications=new_research_specifications,
        )
    )

    planner_message = (
        """Replan the specifications based on the new research specifications."""
    )

    replanned_specifications = llm.invoke(
        [
            SystemMessage(content=specifications_comparison_instructions),
            HumanMessage(content=planner_message),
        ]
    )

    replanned_scope_specifications = ScopeSpecifications(
        scope=scope, specifications=replanned_specifications.content
    )
    return {
        "scope_specifications": replanned_scope_specifications,
        "messages": "Enhacned specifications. Please proceed to procurement",
    }
