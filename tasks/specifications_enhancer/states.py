from typing import Annotated, List, Literal, Optional, Sequence, TypedDict

from dotenv import load_dotenv
from langchain.chat_models import init_chat_model
from langchain.schema import BaseMessage
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeA<PERSON>
from langchain_tavily import TavilySearch
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from langgraph.prebuilt import create_react_agent
from pydantic import BaseModel, Field
from typing_extensions import TypedDict

# from states import ScopeSpecifications


class ScopeSpecifications(BaseModel):
    scope: str = Field(
        description="Name for this scope / topic of the report.",
    )
    specifications: str = Field(
        description="Specifications for the project.",
    )


class SpecificationsEnhancerState(TypedDict):
    is_specifications_clear_enough: bool
    reason: str


class SpecificationsEnhancerInput(TypedDict):
    # Generated by main agent
    topic: str
    scope_specifications: ScopeSpecifications


class EnhancerState(TypedDict):
    # Generated by main agent
    topic: str
    scope_specifications: ScopeSpecifications
    messages: Annotated[Sequence[BaseMessage], add_messages]

    # Localised states
    specifications_enhancer_state: SpecificationsEnhancerState
    essential_specifications: str
    specification_questions: str

    # Generated by human_clarification_agent
    final_answer: str
