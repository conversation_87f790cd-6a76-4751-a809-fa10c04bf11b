import getpass
import os
import pprint
from typing import List

from dotenv import load_dotenv
from langchain_core.messages import BaseMessage, ToolMessage
from langgraph.graph import END, MessageGraph, StateGraph

from tasks.specifications_enhancer.reflexion_agent.chains import (
    first_responder_chain,
    revisor_chain,
)
from tasks.specifications_enhancer.reflexion_agent.execute_tools import execute_tools

# from states import AgentState


load_dotenv()  # take environment variables

if not os.environ.get("GOOGLE_API_KEY"):
    os.environ["GOOGLE_API_KEY"] = getpass.getpass("Enter API key for Google Gemini: ")
if not os.environ.get("TAVILY_API_KEY"):
    os.environ["TAVILY_API_KEY"] = getpass.getpass("Tavily API key:\n")

api_key = os.getenv("GOOGLE_API_KEY")
tavily_api_key = os.getenv("TAVILY_API_KEY")

reflexion_builder = MessageGraph()  # StateGraph(AgentState)
# reflexion_app = StateGraph()
MAX_ITERATIONS = 1


def first_responder_node(state: MessageGraph):
    return state


reflexion_builder.add_node("draft", first_responder_chain)
reflexion_builder.add_node("execute_tools", execute_tools)
reflexion_builder.add_node("revisor", revisor_chain)

reflexion_builder.add_edge("draft", "execute_tools")
reflexion_builder.add_edge("execute_tools", "revisor")


def event_loop(state: List[BaseMessage]) -> str:
    count_tool_visits = sum(isinstance(item, ToolMessage) for item in state)
    num_iterations = count_tool_visits
    if num_iterations > MAX_ITERATIONS:
        return END
    return "execute_tools"


reflexion_builder.add_conditional_edges("revisor", event_loop)
reflexion_builder.set_entry_point("draft")

# reflexion_agent = reflexion_builder.compile()

# print(reflexion_agent.get_graph().draw_ascii())

# events = reflexion_agent.stream("Generate core specifications needed for building 'DEVELOPMENT OF A CUSTOMIZED ROUTE DELIVERY ROBOT'. Context is that this will form the basis to do procurement of components down the line.", stream_mode="messages")
# for event in events:
#     for key, value in event.items():
#         if value is None:
#             continue
#         pprint.pprint(f"Output from node '{key}':")
#         pprint.pprint(value, indent=2, width=80, depth=None)
#         print()

# response = reflexion_agent.invoke("Generate core specifications needed for building 'DEVELOPMENT OF A CUSTOMIZED ROUTE DELIVERY ROBOT'. Context is that this will form the basis to do procurement of components down the line.")
# print(response[-1].tool_calls[0]["args"]["answer"])
# print(response, "response")
