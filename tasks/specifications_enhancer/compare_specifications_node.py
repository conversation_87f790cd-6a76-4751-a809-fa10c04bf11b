from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage

from tasks.specifications_enhancer.states import EnhancerState

specifications_comparison_prompt = """
Given the scope

<scope>
{scope}
<scope>

and the original specifications
<specifications>
{specifications}
<specifications>

and the essential specifications found through research
<research_specifications>
{essential_specifications}
<research_specifications>

compare the two and list down missing specifications and under-specified ones.

Generate a list of {number_of_questions} questions to ask the user to refine the specifications.
Please format it neatly in point form, noting down the question as well any rationale or citations for the question.
"""


def compare_specifications(state: EnhancerState):
    scope_specifications = state["scope_specifications"]
    scope = scope_specifications.scope
    specifications = scope_specifications.specifications
    research_specifications = state["essential_specifications"]

    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    specifications_comparison_instructions = specifications_comparison_prompt.format(
        scope=scope,
        specifications=specifications,
        essential_specifications=research_specifications,
        number_of_questions=7,
    )

    planner_message = """Generate a list of questionsfor the user to answer to refine the specifications based on the research done."""

    response = llm.invoke(
        [
            SystemMessage(content=specifications_comparison_instructions),
            HumanMessage(content=planner_message),
        ]
    )
    return {"specification_questions": response}
