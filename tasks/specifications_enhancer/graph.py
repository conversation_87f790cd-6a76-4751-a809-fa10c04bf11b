from langgraph.graph import START, StateGraph

from tasks.specifications_enhancer.compare_specifications_node import (
    compare_specifications,
)
from tasks.specifications_enhancer.generate_essential_specifications_node import (
    generate_essential_specifications,
)
from tasks.specifications_enhancer.human_clarification_agent import (
    human_clarification_agent_builder,
)
from tasks.specifications_enhancer.states import (
    EnhancerState,
    SpecificationsEnhancerInput,
)
from tasks.specifications_enhancer.summarise_replan_specifications_node import (
    summarise_replan_specifications,
)

specifications_enhancer_builder = StateGraph(
    EnhancerState, input=SpecificationsEnhancerInput
)
specifications_enhancer_builder.add_node(
    "generate_essential_specifications", generate_essential_specifications
)
specifications_enhancer_builder.add_node(
    "compare_specifications", compare_specifications
)
specifications_enhancer_builder.add_node(
    "human_clarification_agent", human_clarification_agent_builder.compile()
)
specifications_enhancer_builder.add_node(
    "summarise_replan_specifications", summarise_replan_specifications
)

specifications_enhancer_builder.add_edge(START, "generate_essential_specifications")
specifications_enhancer_builder.add_edge(
    "generate_essential_specifications", "compare_specifications"
)
specifications_enhancer_builder.add_edge(
    "compare_specifications", "human_clarification_agent"
)
specifications_enhancer_builder.add_edge(
    "human_clarification_agent", "summarise_replan_specifications"
)

specifications_enhancer_app = specifications_enhancer_builder.compile()
# print(specifications_enhancer_app.get_graph().draw_ascii())
# # Add nodes
# report_planner_builder = StateGraph(
#     ReportState,
#     input=ReportStateInput,
#     output=ReportStateOutput,
#     config_schema=Configuration,
# )
