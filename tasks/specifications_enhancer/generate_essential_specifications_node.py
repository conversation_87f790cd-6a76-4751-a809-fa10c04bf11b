from langgraph.graph import START, StateGraph
from langgraph.types import Command

from tasks.specifications_enhancer.compare_specifications_node import (
    compare_specifications,
)
from tasks.specifications_enhancer.reflexion_agent import reflexion_builder
from tasks.specifications_enhancer.states import (
    EnhancerState,
    SpecificationsEnhancerInput,
)
from tasks.specifications_enhancer.summarise_replan_specifications_node import (
    summarise_replan_specifications,
)


def generate_essential_specifications(state: SpecificationsEnhancerInput) -> dict:
    """
    This node's only job is to call the reflexion agent and return the final response
    """
    topic = state["topic"]
    initial_message = f"""
        Generate core specifications needed for building '{topic}'. 
        Context is that this will form the basis to do procurement of components down the line.
        Idea is to understand what specifications is important and needs to be answered, not on the actual answers.
    """

    # Call reflexion agent
    reflexion_graph = reflexion_builder.compile()
    response = reflexion_graph.invoke(initial_message)
    result = response[-1].tool_calls[0]["args"]["answer"]

    # Update your state with the result
    return Command(
        update={"essential_specifications": result},
        goto="compare_specifications",
    )
