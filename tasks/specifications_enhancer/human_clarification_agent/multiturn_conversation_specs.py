import uuid
from typing import Annotated, List, TypedDict

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_groq import <PERSON>tGroq
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph, add_messages
from langgraph.types import Command, interrupt

llm = ChatGroq(model="llama-3.1-8b-instant")


class State(TypedDict):
    # Generated from main caller agent
    specification_questions: str

    # Localised state
    generated_answers: Annotated[List[str], add_messages]
    human_feedback: Annotated[List[str], add_messages]

    # Final output
    final_answer: str


def model(state: State):
    """Here, we're using the LLM to generate a LinkedIn post with human feedback incorporated"""

    print("[model] Generating content")
    specification_questions = state["specification_questions"]
    feedback = (
        state["human_feedback"] if "human_feedback" in state else ["No Feedback yet"]
    )

    # Here, we define the prompt

    prompt = f"""

        Specification Questions {specification_questions}
        Human Feedback: {feedback[-1] if feedback else "No feedback yet"}

        Generate structured and well-formatted answers based on the given questions.
        If no human feedback is provided, generate the answers based on specifications and explicitly inform the user that these are assumed answers.

        Consider previous human feedback to refine the reponse.
        Do not give any coding answers.
    """

    response = llm.invoke(
        [
            SystemMessage(content="You are an expert Technical consultant"),
            HumanMessage(content=prompt),
        ]
    )

    geneated_linkedin_post = response.content

    print(f"[model_node] Generated answers:\n{geneated_linkedin_post}\n")

    return {
        "generated_answers": [AIMessage(content=geneated_linkedin_post)],
        "human_feedback": feedback,
    }


def human_node(state: State):
    """Human Intervention node - loops back to model unless input is done"""

    print("\n [human_node] awaiting human feedback...")

    generated_answers = state["generated_answers"]

    # Interrupt to get user feedback

    user_feedback = interrupt(
        {
            "generated_answers": generated_answers,
            "message": "Provide feedback or type 'done' to finish",
        }
    )

    print(f"[human_node] Received human feedback: {user_feedback}")

    # If user types "done", transition to END node
    if user_feedback.lower() == "done":
        return Command(
            update={"human_feedback": state["human_feedback"] + ["Finalised"]},
            goto="end_node",
        )

    # Otherwise, update feedback and return to model for re-generation
    return Command(
        update={"human_feedback": state["human_feedback"] + [user_feedback]},
        goto="model",
    )


def end_node(state: State):
    """Final node"""
    print("\n[end_node] Process finished")
    print("Final Generated Post:", state["generated_answers"][-1])
    print("Final Human Feedback", state["human_feedback"])
    return {"final_answer": state["generated_answers"][-1]}


# Buiding the Graph

human_clarification_agent_builder = StateGraph(State)
human_clarification_agent_builder.add_node("model", model)
human_clarification_agent_builder.add_node("human_node", human_node)
human_clarification_agent_builder.add_node("end_node", end_node)

human_clarification_agent_builder.set_entry_point("model")

# Define the flow

human_clarification_agent_builder.add_edge(START, "model")
human_clarification_agent_builder.add_edge("model", "human_node")

human_clarification_agent_builder.set_finish_point("end_node")

# # Enable Interrupt mechanism
# checkpointer = MemorySaver()
# app = human_clarification_agent_builder.compile(checkpointer=checkpointer)

# thread_config = {"configurable": {
#     "thread_id": uuid.uuid4()
# }}

# specification_questions = """"
#     "What is the dimensions of the office space?",
#     "Is the space narrow for traversing?",
#     "What is the expected weight of the packages the robot will be delivering?",
#     "Is the robot communicated via Bluetooth?"
# """
# initial_state = {
#     "specification_questions": specification_questions,
#     "generated_answers": [],
#     "human_feedback": []
# }


# for chunk in app.stream(initial_state, config=thread_config):
#     for node_id, value in chunk.items():
#         #  If we reach an interrupt, continuously ask for human feedback

#         if(node_id == "__interrupt__"):
#             while True:
#                 user_feedback = input("Provide feedback (or type 'done' when finished): ")

#                 # Resume the graph execution with the user's feedback
#                 app.invoke(Command(resume=user_feedback), config=thread_config)

#                 # Exit loop if user says done
#                 if user_feedback.lower() == "done":
#                     break
