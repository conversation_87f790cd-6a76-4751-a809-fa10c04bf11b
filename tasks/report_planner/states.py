import operator
from typing import Annotated, List, Literal, Sequence, TypedDict

from langchain.schema import BaseMessage
from langgraph.graph.message import add_messages
from pydantic import BaseModel, Field


class ScopeSpecifications(BaseModel):
    scope: str = Field(
        description="Name for this scope / topic of the report.",
    )
    specifications: str = Field(
        description="Specifications for the project.",
    )


class Section(BaseModel):
    name: str = Field(
        description="Name for this section of the report.",
    )
    description: str = Field(
        description="Brief overview of the main topics and concepts to be covered in this section.",
    )
    research: bool = Field(
        description="Whether to perform web research for this section of the report."
    )
    content: str = Field(description="The content of the section.")


class Sections(BaseModel):
    sections: List[Section] = Field(
        description="Sections of the report.",
    )


class SearchQuery(BaseModel):
    search_query: str = Field(None, description="Query for web search.")


class Queries(BaseModel):
    queries: List[SearchQuery] = Field(
        description="List of search queries.",
    )


class Feedback(BaseModel):
    grade: Literal["pass", "fail"] = Field(
        description="Evaluation result indicating whether the response meets requirements ('pass') or needs revision ('fail')."
    )
    follow_up_queries: List[SearchQuery] = Field(
        description="List of follow-up search queries.",
    )


class HardwareQuery(BaseModel):
    component_name: str = Field(description="Name for this hardware component.")
    search_query: str = Field(
        description="Search query to find relevant information about this hardware component."
    )
    specification: str = Field(
        description="Technical specifications and key features of the hardware."
    )
    list_device_name_refer: List[str] = Field(
        description="List of device names that are relevant to this hardware component."
    )


class HardWareQueries(BaseModel):
    hardware_queries: List[HardwareQuery] = Field(
        description="List of hardware components for the project."
    )


class ReportStateInput(TypedDict):
    topic: str  # Report topic
    scope_specifications: ScopeSpecifications


class ReportStateOutput(TypedDict):
    hardware_recommendation_search: str  #
    messages: Annotated[Sequence[BaseMessage], add_messages]


class ReportState(TypedDict):
    topic: str  # Report topic
    scope_specifications: ScopeSpecifications
    feedback_on_report_plan: str  # Feedback on the report plan
    sections: list[Section]  # List of report sections
    completed_sections: Annotated[list, operator.add]  # Send() API key
    report_sections_from_research: (
        str  # String of any completed sections from research to write final sections
    )
    final_report: str  # Final report
    hardware_queries: HardWareQueries
    hardware_recommendation_search: str
    messages: Annotated[Sequence[BaseMessage], add_messages]


class SectionState(TypedDict):
    topic: str  # Report topic
    scope_specifications: ScopeSpecifications
    section: Section  # Report section
    search_iterations: int  # Number of search iterations done
    search_queries: list[SearchQuery]  # List of search queries
    search_results: list  # String of formatted source content from web search
    summarization_source: str
    report_sections_from_research: (
        str  # String of any completed sections from research to write final sections
    )
    completed_sections: list[
        Section
    ]  # Final key we duplicate in outer state for Send() API


class SectionOutputState(TypedDict):
    completed_sections: list[
        Section
    ]  # Final key we duplicate in outer state for Send() API
