from typing import Any, Literal

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.constants import Send
from langgraph.graph import END, START
from langgraph.types import Command, interrupt

from tasks.report_planner.configuration import Configuration
from tasks.report_planner.prompts import (
    final_section_academic_style_instructions,
    final_section_writer_instructions,
    section_grader_instructions,
    section_writer_instructions,
)
from tasks.report_planner.states import Feedback, ReportState, SectionState
from tasks.report_planner.utils import format_sections, get_config_value


def write_section(
    state: SectionState, config: RunnableConfig
) -> Command[Literal[END, "search_web"]]:
    """Write a section of the report"""

    # Get state
    scope_specifications = state["scope_specifications"]
    section = state["section"]
    summarization_source = state["summarization_source"]

    # Get configuration
    configurable = Configuration.from_runnable_config(config)

    # Format system instructions
    system_instructions = section_writer_instructions.format(
        scope=scope_specifications.scope,
        section_name=section.name,
        section_topic=section.description,
        context=summarization_source,
        section_content=section.content,
    )

    # Generate section
    writer_provider = get_config_value(configurable.writer_provider)
    writer_model_name = get_config_value(configurable.writer_model)
    writer_model = init_chat_model(
        model=writer_model_name, model_provider=writer_provider, temperature=0
    )
    section_content = writer_model.invoke(
        [
            SystemMessage(content=system_instructions),
            HumanMessage(
                content="Generate a report section based on the provided sources."
            ),
        ]
    )

    # Write content to the section object
    section.content = section_content.content

    # Grade prompt
    section_grader_message = """Grade the report and consider follow-up questions for missing information.
                               If the grade is 'pass', return empty strings for all follow-up queries.
                               If the grade is 'fail', provide specific search queries to gather missing information."""

    section_grader_instructions_formatted = section_grader_instructions.format(
        scope=scope_specifications.scope,
        section_topic=section.description,
        section=section.content,
        number_of_follow_up_queries=configurable.number_of_queries,
    )

    # Use planner model for reflection
    planner_provider = get_config_value(configurable.planner_provider)
    planner_model = get_config_value(configurable.planner_model)

    # If the planner model is claude-3-7-sonnet-latest, we need to use bind_tools to use thinking when generating the feedback
    if planner_model == "claude-3-7-sonnet-latest":
        # Allocate a thinking budget for claude-3-7-sonnet-latest as the planner model
        reflection_model = init_chat_model(
            model=planner_model,
            model_provider=planner_provider,
            max_tokens=20_000,
            thinking={"type": "enabled", "budget_tokens": 16_000},
        )

        # with_structured_output uses forced tool calling, which thinking mode with Claude 3.7 does not support
        # So, we use bind_tools without enforcing tool calling to generate the report sections
        reflection_result = reflection_model.bind_tools([Feedback]).invoke(
            [
                SystemMessage(content=section_grader_instructions_formatted),
                HumanMessage(content=section_grader_message),
            ]
        )
        tool_call = reflection_result.tool_calls[0]["args"]
        feedback = Feedback.model_validate(tool_call)

    else:
        reflection_model = init_chat_model(
            model=planner_model, model_provider=planner_provider
        ).with_structured_output(Feedback)

        feedback = reflection_model.invoke(
            [
                SystemMessage(content=section_grader_instructions_formatted),
                HumanMessage(content=section_grader_message),
            ]
        )

    # If the section is passing or the max search depth is reached, publish the section to completed sections
    if (
        feedback.grade == "pass"
        or state["search_iterations"] >= configurable.max_search_depth
    ):
        # Publish the section to completed sections
        return Command(update={"completed_sections": [section]}, goto=END)
    # Update the existing section with new content and update search queries
    else:
        return Command(
            update={
                "search_queries": feedback.follow_up_queries,
                "section": section,
            },
            goto="search_web",
        )


def rewrite_chapter_sections(state: SectionState, config: RunnableConfig):
    """Write final sections of the report, which do not require web search and use the completed sections as context"""

    # Get configuration
    configurable = Configuration.from_runnable_config(config)

    # Get state
    section = state["section"]
    completed_report_sections = state["report_sections_from_research"]

    # Format system instructions
    system_instructions = final_section_writer_instructions.format(
        section_name=section.name,
        section_topic=section.description,
        context=completed_report_sections,
    )

    # Generate section
    writer_provider = get_config_value(configurable.writer_provider)
    writer_model_name = get_config_value(configurable.writer_model)
    writer_model = init_chat_model(
        model=writer_model_name, model_provider=writer_provider, temperature=0
    )
    section_content = writer_model.invoke(
        [
            SystemMessage(content=system_instructions),
            HumanMessage(
                content="Generate a report section based on the provided sources."
            ),
        ]
    )

    # Write content to section
    section.content = section_content.content

    # Write the updated section to completed sections
    return {"completed_sections": [section]}


def gather_completed_sections(state: ReportState):
    """Gather completed sections from research and format them as context for writing the final sections"""

    # List of completed sections
    completed_sections = state["completed_sections"]

    # Format completed section to str to use as context for final sections
    completed_report_sections = format_sections(completed_sections)

    return {"report_sections_from_research": completed_report_sections}


def initiate_final_section_writing(state: ReportState):
    """Write any final sections using the Send API to parallelize the process"""

    # Kick off section writing in parallel via Send() API for any sections that do not require research
    return [
        Send(
            "rewrite_chapter_sections",
            {
                "topic": state["topic"],
                "section": s,
                "report_sections_from_research": state["report_sections_from_research"],
            },
        )
        for s in state["sections"]
        if not s.research
    ]


def write_final_report_academic_style(state: ReportState, config: RunnableConfig):
    """Compile the final report"""

    # Get sections
    sections = state["sections"]

    # Get configuration
    configurable = Configuration.from_runnable_config(config)

    completed_sections = {s.name: s.content for s in state["completed_sections"]}
    # Update sections with completed content while maintaining original order
    for section in sections:
        section.content = completed_sections[section.name]

    # Compile final report
    all_sections = "\n\n".join([s.content for s in sections])

    # Format system instructions
    system_instructions = final_section_academic_style_instructions.format(
        report=all_sections
    )

    # Generate section
    writer_provider = get_config_value(configurable.writer_provider)
    writer_model_name = get_config_value(configurable.writer_model)
    writer_model = init_chat_model(
        model=writer_model_name, model_provider=writer_provider, temperature=0
    )
    section_content = writer_model.invoke(
        [
            SystemMessage(content=system_instructions),
            HumanMessage(
                content="You are a scientific researcher tasked with rewriting the following report in a formal and academic writing style."
            ),
        ]
    )

    return {"final_report": section_content.content}
