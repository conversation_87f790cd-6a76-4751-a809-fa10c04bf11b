from typing import Literal

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.constants import Send
from langgraph.types import Command, interrupt

from tasks.report_planner.configuration import Configuration
from tasks.report_planner.prompts import report_planner_instructions
from tasks.report_planner.states import ReportState, Sections
from tasks.report_planner.utils import get_config_value


def generate_report_plan(state: ReportState, config: RunnableConfig):
    """Generate the report plan"""

    # Inputs
    scope_specifications = state["scope_specifications"]
    feedback = state.get("feedback_on_report_plan", None)
    # Get configuration
    configurable = Configuration.from_runnable_config(config)
    report_structure = configurable.report_structure
    # Convert JSON object to string if necessary
    if isinstance(report_structure, dict):
        report_structure = str(report_structure)

    # Format system instructions
    system_instructions_sections = report_planner_instructions.format(
        scope=scope_specifications.scope,
        specifications=scope_specifications.specifications,
        report_organization=report_structure,
        feedback=feedback,
    )

    # Set the planner
    planner_provider = get_config_value(configurable.planner_provider)
    planner_model = get_config_value(configurable.planner_model)

    # Report planner instructions
    planner_message = """Generate the sections of the report. Your response must include a 'sections' field containing a list of sections. 
                        Each section must have: name, description, plan, research, and content fields."""

    # Run the planner
    if planner_model == "claude-3-7-sonnet-latest":
        # Allocate a thinking budget for claude-3-7-sonnet-latest as the planner model
        planner_llm = init_chat_model(
            model=planner_model,
            model_provider=planner_provider,
            max_tokens=20_000,
            thinking={"type": "enabled", "budget_tokens": 16_000},
        )

        # with_structured_output uses forced tool calling, which thinking mode with Claude 3.7 does not support
        # So, we use bind_tools without enforcing tool calling to generate the report sections
        report_sections = planner_llm.bind_tools([Sections]).invoke(
            [
                SystemMessage(content=system_instructions_sections),
                HumanMessage(content=planner_message),
            ]
        )
        tool_call = report_sections.tool_calls[0]["args"]
        report_sections = Sections.model_validate(tool_call)

    else:
        # With other models, we can use with_structured_output
        planner_llm = init_chat_model(
            model=planner_model, model_provider=planner_provider
        )
        structured_llm = planner_llm.with_structured_output(Sections)
        report_sections = structured_llm.invoke(
            [
                SystemMessage(content=system_instructions_sections),
                HumanMessage(content=planner_message),
            ]
        )

    # Get sections
    sections = report_sections.sections

    return {"sections": sections}


async def human_feedback(
    state: ReportState, config: RunnableConfig
) -> Command[Literal["generate_report_plan", "build_section_with_web_research"]]:
    """Get feedback on the report plan"""

    # Get sections
    topic = state["topic"]
    scope_specifications = state["scope_specifications"]
    sections = state["sections"]
    sections_str = "\n\n".join(
        f"Section: {section.name}\n"
        f"Description: {section.description}\n"
        f"Research needed: {'Yes' if section.research else 'No'}\n"
        for section in sections
    )

    # Get feedback on the report plan from interrupt
    # interrupt_message = f"""Please provide feedback on the following report plan.
    #                     \n\n{sections_str}\n\n
    #                     \nDoes the report plan meet your needs? Pass 'true' to approve the report plan or provide feedback to regenerate the report plan:"""

    # feedback = interrupt(interrupt_message)

    return Command(
        goto=[
            Send(
                "build_section_with_web_research",
                {
                    "topic": topic,
                    "scope_specifications": scope_specifications,
                    "section": s,
                    "search_iterations": 0,
                },
            )
            for s in sections
            if s.research
        ]
    )

    # If the user approves the report plan, kick off section writing
    if isinstance(feedback, bool) and feedback is True:
        # Treat this as approve and kick off section writing
        return Command(
            goto=[
                Send(
                    "build_section_with_web_research",
                    {
                        "topic": topic,
                        "scope_specifications": scope_specifications,
                        "section": s,
                        "search_iterations": 0,
                    },
                )
                for s in sections
                if s.research
            ]
        )

    # If the user provides feedback, regenerate the report plan
    elif isinstance(feedback, str):
        # Treat this as feedback
        return Command(
            goto="generate_report_plan",
            update={"feedback_on_report_plan": feedback},
        )
    else:
        raise TypeError(f"Interrupt value of type {type(feedback)} is not supported.")
