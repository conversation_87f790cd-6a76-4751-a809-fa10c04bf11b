from typing import Any

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.runnables import RunnableConfig

from tasks.report_planner.configuration import Configuration
from tasks.report_planner.prompts import hardware_queries_instruction
from tasks.report_planner.states import HardWareQueries, ReportState
from tasks.report_planner.utils import get_config_value


async def generate_hardware_queries(state: ReportState, config: RunnableConfig):
    """Generate the report plan"""

    # Inputs
    scope_specifications = state["scope_specifications"]
    final_report = state["final_report"]
    # Get configuration
    configurable = Configuration.from_runnable_config(config)
    report_structure = configurable.report_structure
    # Convert JSON object to string if necessary
    if isinstance(report_structure, dict):
        report_structure = str(report_structure)

    # Format system instructions
    system_instructions_sections = hardware_queries_instruction.format(
        scope=scope_specifications.scope,
        specifications=scope_specifications.specifications,
        report=final_report,
    )

    # Set the planner
    planner_provider = get_config_value(configurable.planner_provider)
    planner_model = get_config_value(configurable.planner_model)

    # Run the planner
    if planner_model == "claude-3-7-sonnet-latest":
        # Allocate a thinking budget for claude-3-7-sonnet-latest as the planner model
        planner_llm = init_chat_model(
            model=planner_model,
            model_provider=planner_provider,
            max_tokens=20_000,
            thinking={"type": "enabled", "budget_tokens": 16_000},
        )

        # with_structured_output uses forced tool calling, which thinking mode with Claude 3.7 does not support
        # So, we use bind_tools without enforcing tool calling to generate the report sections
        hardware_content = planner_llm.bind_tools([HardWareQueries]).invoke(
            [
                SystemMessage(content=system_instructions_sections),
                HumanMessage(
                    content="detailed list of all necessary hardware components to facilitate online price research for specification and technical report project."
                ),
            ]
        )
        tool_call = hardware_content.tool_calls[0]["args"]
        hardware_content = HardWareQueries.model_validate(tool_call)

    else:
        # With other models, we can use with_structured_output
        planner_llm = init_chat_model(
            model=planner_model, model_provider=planner_provider
        )
        structured_llm = planner_llm.with_structured_output(HardWareQueries)
        hardware_content = structured_llm.invoke(
            [
                SystemMessage(content=system_instructions_sections),
                HumanMessage(
                    content="detailed list of all necessary hardware components to facilitate online price research for specification and technical report project."
                ),
            ]
        )
    # Get sections
    generate_hardware_queries = hardware_content.hardware_queries

    return {"hardware_queries": generate_hardware_queries}
