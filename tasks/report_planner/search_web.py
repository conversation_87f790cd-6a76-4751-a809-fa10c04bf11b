from typing import Any

from langchain.chat_models import init_chat_model
from langchain_core.runnables import RunnableConfig

from search_engine import arxiv_search_async, perplexity_search, tavily_search_async
from tasks.report_planner.configuration import Configuration
from tasks.report_planner.states import SectionState
from tasks.report_planner.utils import get_config_value, get_search_params


async def search_web(state: SectionState, config: RunnableConfig):
    """Search the web for each query, then return a list of raw sources and a formatted string of sources."""
    # Get state
    search_queries = state["search_queries"]

    # Get configuration
    configurable = Configuration.from_runnable_config(config)
    search_api = get_config_value(configurable.search_api)
    search_api_config = (
        configurable.search_api_config or {}
    )  # Get the config dict, default to empty
    params_to_pass = get_search_params(
        search_api, search_api_config
    )  # Filter parameters

    # Web search
    query_list = [query.search_query for query in search_queries]

    # Search the web with parameters
    if search_api == "tavily":
        search_results = await tavily_search_async(query_list, **params_to_pass)
    elif search_api == "perplexity":
        search_results = perplexity_search(query_list, **params_to_pass)
    elif search_api == "arxiv":
        search_results = await arxiv_search_async(query_list, **params_to_pass)
    else:
        raise ValueError(f"Unsupported search API: {search_api}")

    return {
        "search_results": search_results,
    }
