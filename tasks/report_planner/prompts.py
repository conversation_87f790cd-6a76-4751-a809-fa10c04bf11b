# Prompt to extract scope and specifications for a project
topic_extraction_instructions = """ 

<context>
{context}
</context>

The scope should be a high-level overview of the main topics covered in the report, while the specifications contain requirements for the project with bullet points format.
"""

# Prompt to generate the report plan
report_planner_instructions = """I want a plan for a report that is concise and focused.

<Report Topic>
{scope}
</Report Topic>

<Specifications for project>
{specifications}
</Specifications for project>

<Task>
Generate a list of sections for the report. Your plan should be tight and focused with NO overlapping sections or unnecessary filler. 
The plan should comprehensively cover all aspects from the specifications

For example, a good report structure might look like:

<report organization>
{report_organization}
</report organization>

Each section should have the fields:

- Name - Name for this section of the report.
- Description - Brief overview of the main topics covered in this section.
- Research - Whether to perform web research for this section of the report. ( shoule be Yes for technical sections)
- Content - The content of the section, which you will leave blank for now.

Integration guidelines:
- Include examples and implementation details within main topic sections, not as separate sections
- Ensure each section has a distinct purpose with no content overlap
- Combine related concepts rather than separating them

Before submitting, review your structure to ensure it has no redundant sections and follows a logical flow.
</Task>

<Feedback>
Here is feedback on the report structure from review (if any):
{feedback}
</Feedback>
"""

# Prompt to extract content from a source

source_content_summarization_instructions = """ 
Below is the query that you can use to get the content of the paper. and the content of the paper.

Please extract/summary the content of the paper that related to the query.

Do not include word count or any preamble in your response,

<query>
{query}
</query>

Below is the content of the paper.

<content>
{content}
</content>

Please extract the content of the paper that related to the query.
Do not include word count or any preamble in your response,
"""

# Query writer instructions
query_writer_instructions = """You are an expert technical writer crafting targeted web search queries that will gather comprehensive information for writing a technical report section.

<Report Topic>
{scope}
</Report Topic>

<Section topic>
{section_topic}
</Section topic>

<Task>
Your goal is to generate {number_of_queries} search queries that will help gather comprehensive information above the section topic. 

Search queries can be based on the specifications below (optional but can be referenced, especially for the Hardware chapter).

<Specifications>
{specifications}
</Specifications>

The queries should:
1. Be related to the topic 
2. Examine different aspects of the topic

Make the queries specific enough to find high-quality, relevant sources.
</Task>
"""

# Section writer instructions
section_writer_instructions = """You are an expert technical writer crafting one section of a technical report.

<Report topic>
{scope}
</Report topic>

<Section name>
{section_name}
</Section name>

<Section topic>
{section_topic}
</Section topic>

<Existing section content (if populated)>
{section_content}
</Existing section content>

<Source material>
{context}
</Source material>

<Guidelines for writing>
1. If the existing section content is not populated, write a new section from scratch.
2. If the existing section content is populated, write a new section that synthesizes the existing section content with the Source material.
</Guidelines for writing>

<Length and style>
- No marketing language
- Technical focus
- Write in simple, clear language
- Start with your most important insight in **bold**
- Use short paragraphs (2-3 sentences max)
- Use ## for section title (Markdown format)
- Only use ONE structural element IF it helps clarify your point:
  * Either a focused table comparing 2-3 key items (using Markdown table syntax)
  * Or a short list (3-5 items) using proper Markdown list syntax:
    - Use `*` or `-` for unordered lists
    - Use `1.` for ordered lists
    - Ensure proper indentation and spacing
- Sources cited in line if any information is drawn from the provided sources
  - Use the format [order_number](link) , order_number start from 1

- End with ### Sources that references the below source material formatted as:
  * List each source with title, date, and URL 
  * Format: `order_number - Title : URL` order_number start from 1
</Length and style>

<Quality checks>
- Exactly 150-200 words, 350-400 words for Hardware sections (excluding title and sources)
- Careful use of only ONE structural element (table or list) and only if it helps clarify your point
- One specific example / case study
- Starts with bold insight
- No preamble prior to creating the section content
- Sources cited in line with the provided format with markdown syntax Use the format [order_number](link) , (e.g., [1](link), [2](link), etc.)
- Sources cited at end
</Quality checks>
"""

# Instructions for section grading
section_grader_instructions = """Review a report section relative to the specified topic and specifications for the project.

<Report topic>
{scope}
</Report topic>

<section topic>
{section_topic}
</section topic>

<section content>
{section}
</section content>

<task>
Evaluate whether the section content adequately addresses the section topic.

If the section content does not adequately address the section topic, generate {number_of_follow_up_queries} follow-up search queries to gather missing information.
</task>

<format>
    grade: Literal["pass","fail"] = Field(
        description="Evaluation result indicating whether the response meets requirements ('pass') or needs revision ('fail')."
    )
    follow_up_queries: List[SearchQuery] = Field(
        description="List of follow-up search queries.",
    )
</format>
"""

final_section_writer_instructions = """You are an expert technical writer crafting a section that synthesizes information from the rest of the report.

<Section name>
{section_name}
</Section name>

<Section topic> 
{section_topic}
</Section topic>

<Available report content>
{context}
</Available report content>

<Task>
1. Section-Specific Approach:

For Introduction/System Overview:
- Use # for report title (Markdown format)
- 100-150 word limit
- Write in simple and clear language
- Focus on the core motivation for the report in 1-2 paragraphs
- Use a clear narrative arc to introduce the report
- Include NO structural elements (no lists or tables)
- No sources section needed

For Conclusion/Summary:
- Use ## for section title (Markdown format)
- 100-150 word limit
- For comparative reports:
    * Must include a focused comparison table using Markdown table syntax
    * Table should distill insights from the report
    * Keep table entries clear and concise
- For non-comparative reports: 
    * Only use ONE structural element IF it helps distill the points made in the report:
    * Either a focused table comparing items present in the report (using Markdown table syntax)
    * Or a short list using proper Markdown list syntax:
      - Use `*` or `-` for unordered lists
      - Use `1.` for ordered lists
      - Ensure proper indentation and spacing
- End with specific next steps or implications
- No sources section needed

3. Writing Approach:
- Use concrete details over general statements
- Make every word count
- Focus on your single most important point
</Task>

<Quality Checks>
- For introduction: 100-150 word limit, # for report title, no structural elements, no sources section
- For conclusion: 100-150 word limit, ## for section title, only ONE structural element at most, no sources section
- Markdown format
- Do not include word count or any preamble in your response
</Quality Checks>"""


final_section_academic_style_instructions = """
You are a scientific researcher tasked with rewriting the following report in a formal and academic writing style.
 Ensure clarity, precision, and coherence throughout the text. 
 Rewrite citations using sequential numbering from 1 to number_of_sources (e.g., [1](link), [2](link), etc.)
 Consolidate all sources into a single reference section at the end of the report, rather than listing them separately at the end of each chapter.

<Report>
{report}
</Report>

Do not include word count or any preamble in your response
"""

# Hardware recommendation instructions
hardware_queries_instruction = """You are a technical expert responsible for generating a comprehensive list of hardware components for a specification and technical report project.

<Report Topic>
{scope}
</Report Topic>

<Project Specifications>
{specifications}
</Project Specifications>

<Technical Report>
{report}
</Technical Report>

Please provide a detailed list of all necessary hardware components to facilitate search on E-commerce platform.
If a component consists of multiple sub-components, list them separately.

For each component, include:

A single E-commerce query optimized for general research (avoid overly specific queries that may not yield relevant results).
A specification summary for the project in bullet point format.
List of device name refer

Each hardware component should be structured as follows:

    - Component Name - Nmae of the hardware component
    - Search Query - E-commerce query
    - Specifications - bullet points list of specifications
    - List of device name refer - List of device names that are relevant to this hardware component

Before submitting, review your structure to ensure it has no redundant sections and follows a logical flow.


"""
