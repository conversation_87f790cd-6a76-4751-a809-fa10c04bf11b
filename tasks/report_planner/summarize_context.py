import asyncio
import random
import time
from typing import Any

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.runnables import RunnableConfig

from tasks.report_planner.configuration import Configuration
from tasks.report_planner.prompts import source_content_summarization_instructions
from tasks.report_planner.states import SectionState
from tasks.report_planner.utils import format_sources, get_config_value


async def summarize_relevant_content_from_source(
    state: SectionState, config: RunnableConfig
):
    """Extract the most relevant content from a source with rate limit handling"""
    search_results = state["search_results"]

    # Get configuration
    configurable = Configuration.from_runnable_config(config)
    max_tokens_per_source = configurable.max_tokens_per_source
    summarization_provider = get_config_value(configurable.summarization_provider)
    summarization_model_name = get_config_value(configurable.summarization_model)

    # Initialize model only once
    summarization_model = init_chat_model(
        model=summarization_model_name,
        model_provider=summarization_provider,
        temperature=0,
        max_tokens=3000,
    )

    # Add retry configuration
    max_retries = 3  # Maximum number of retries
    base_delay = 1  # Base delay in seconds
    max_delay = 60  # Maximum delay in seconds

    async def summarize_single_source(query: str, raw_content: str):
        retries = 0
        while True:
            try:
                response = await asyncio.to_thread(
                    summarization_model.invoke,
                    [
                        SystemMessage(
                            content=source_content_summarization_instructions.format(
                                query=query,
                                content=raw_content,
                            )
                        ),
                        HumanMessage(
                            content="Please extract the content of the paper that related to the query."
                        ),
                    ],
                )
                response_content = response.content
                # Extract content after </think> if present
                if "</think>" in response_content:
                    response_content = response_content.split("</think>")[1].strip()
                return response_content

            except Exception as e:
                retries += 1
                if retries > max_retries:
                    raise Exception(f"Maximum retries exceeded for rate limit: {e}")

                # Calculate exponential backoff with jitter
                delay = min(max_delay, base_delay * (2**retries))
                jitter = random.uniform(0, 0.1 * delay)  # 10% jitter
                wait_time = delay + jitter

                print(
                    f"Rate limit hit. Retrying in {wait_time:.2f} seconds... (Attempt {retries}/{max_retries})"
                )
                await asyncio.sleep(wait_time)

            except Exception as e:
                print(f"Error during summarization: {e}")
                raise

    # Create a flat list of all summarization tasks
    summarization_tasks = []
    for i, search_result in enumerate(search_results):
        query = search_result["query"]
        for j, result in enumerate(search_result["results"]):
            summarization_tasks.append((i, j, query, result["raw_content"]))

    # Process summarizations with concurrency control
    async def process_task(task):
        i, j, query, raw_content = task
        try:
            summary = await summarize_single_source(query, raw_content)
            return (i, j, summary, None)
        except Exception as e:
            # Return error to handle later
            return (i, j, None, str(e))

    # Add semaphore to limit concurrent API calls
    semaphore = asyncio.Semaphore(5)  # Adjust based on API limits

    async def bounded_process_task(task):
        async with semaphore:
            return await process_task(task)

    # Run all summarization tasks with controlled concurrency
    results = await asyncio.gather(
        *(bounded_process_task(task) for task in summarization_tasks)
    )

    # Update the search_results with the summaries
    failed_tasks = []
    for i, j, summary, error in results:
        if error is None:
            search_results[i]["results"][j]["summarization_raw_content"] = summary
        else:
            failed_tasks.append((i, j, error))
            # Set a placeholder for failed summarizations
            search_results[i]["results"][j][
                "summarization_raw_content"
            ] = f"[Summarization failed: {error}]"

    # Log any failures
    if failed_tasks:
        print(f"Warning: {len(failed_tasks)} summarization tasks failed:")
        for i, j, error in failed_tasks:
            print(f"  - Source {i}, Result {j}: {error}")

    format_source = format_sources(search_results, max_tokens_per_source)
    return {
        "summarization_source": format_source,
        "search_iterations": state["search_iterations"] + 1,
    }
