# from linkup import LinkupClient
from typing import Any, Dict, Optional

import pandas as pd
from langsmith import traceable
from tabulate import tabulate

from tasks.report_planner.states import Section


def get_config_value(value):
    """
    Helper function to handle both string and enum cases of configuration values
    """
    return value if isinstance(value, str) else value.value


# Helper function to get search parameters based on the search API and config
def get_search_params(
    search_api: str, search_api_config: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Filters the search_api_config dictionary to include only parameters accepted by the specified search API.

    Args:
        search_api (str): The search API identifier (e.g., "exa", "tavily").
        search_api_config (Optional[Dict[str, Any]]): The configuration dictionary for the search API.

    Returns:
        Dict[str, Any]: A dictionary of parameters to pass to the search function.
    """
    # Define accepted parameters for each search API
    SEARCH_API_PARAMS = {
        "tavily": ["number_sources_per_query"],  # no additional parameters
        "perplexity": [],
        "arxiv": [
            "load_max_docs",
            "number_sources_per_query",
            "get_full_documents",
            "load_all_available_meta",
        ],
    }

    # Get the list of accepted parameters for the given search API
    accepted_params = SEARCH_API_PARAMS.get(search_api, [])

    # If no config provided, return an empty dict
    if not search_api_config:
        return {}

    # Filter the config to only include accepted parameters
    return {k: v for k, v in search_api_config.items() if k in accepted_params}


def format_sources(summarize_search_response, max_tokens_per_source):
    """
    Takes a list of search responses and formats them into a readable string.
    Limits the raw_content to approximately max_tokens_per_source.

    Args:
        search_responses: List of search response dicts+, each containing:
            - query: str
            - results: List of dicts with fields:
                - title: str
                - url: str
                - content: str
                - score: float
                - raw_content: str|None
        max_tokens_per_source: int
        include_raw_content: bool

    Returns:
        str: Formatted string with deduplicated sources
    """
    # Collect all results
    sources_list = []
    for response in summarize_search_response:
        sources_list.extend(response["results"])

    # Format output
    formatted_text = "Sources:\n\n"
    for i, source in enumerate(sources_list, 1):
        formatted_text += f"Source {source['title']}:\n===\n"
        formatted_text += f"URL: {source['url']}\n===\n"
        # Using rough estimate of 4 characters per token
        char_limit = max_tokens_per_source * 4
        # Handle None raw_content
        summarization_raw_content = source.get("summarization_raw_content", "")
        if summarization_raw_content is None:
            summarization_raw_content = ""
            print(f"Warning: No raw_content found for source {source['url']}")
        if len(summarization_raw_content) > char_limit:
            summarization_raw_content = (
                summarization_raw_content[:char_limit] + "... [truncated]"
            )
        formatted_text += (
            f"summarization source content : {summarization_raw_content}\n\n"
        )

    return formatted_text.strip()


def format_sections(sections: list[Section]) -> str:
    """Format a list of sections into a string"""
    formatted_str = ""
    for idx, section in enumerate(sections, 1):
        formatted_str += f"""
{'='*60}
Section {idx}: {section.name}
{'='*60}
Description:
{section.description}
Requires Research: 
{section.research}

Content:
{section.content if section.content else '[Not yet written]'}

"""
    return formatted_str


def convert_to_markdown_format(hardware_search_results):
    markdown_str = "# HARDWARE RECOMMENDATION\n\n"

    for i, item in enumerate(hardware_search_results):
        markdown_str += f"## {i+1}. {item['component_name']}\n\n"
        markdown_str += f"**Search Query** : \n `{item['search_query']}`\n\n"
        markdown_str += f"**Specification** : \n {item['specification']}\n\n"
        markdown_str += (
            f"**List of Device Name Refer** :  {item['list_device_name_refer_llm']}\n\n"
        )
        search_results = item["result_search"]
        markdown_str += f"**List items from Amazon** :  \n\n"
        if search_results:
            df = pd.DataFrame(search_results)
            # Select and rename columns
            if not df.empty:
                columns_to_keep = [
                    "asin",
                    "url",
                    "title",
                    "price",
                    "rating",
                    "reviews_count",
                ]
                columns_to_display = [
                    col for col in columns_to_keep if col in df.columns
                ]

                if columns_to_display:
                    df_display = df[columns_to_display].copy()
                    # Rename columns for display
                    df_display = df_display.sort_values(by="rating", ascending=False)
                    df_display = df_display.head(10)
                    column_rename = {
                        "asin": "Product ID",
                        "title": "Product Description",
                        "price": "Price (SGD)",
                        "rating": "Rating",
                        "reviews_count": "Reviews",
                    }
                    # add ID column
                    df_display.rename(
                        columns={
                            col: column_rename.get(col, col)
                            for col in columns_to_display
                        },
                        inplace=True,
                    )
                    df_display["Product ID"] = df_display.apply(
                        lambda row: f"[{row['Product ID']}]({row['url']})",
                        axis=1,
                    )

                    # replace ~ by - in column Product Description
                    df_display["Product Description"] = df_display[
                        "Product Description"
                    ].str.replace("~", "-")
                    # remove url column
                    df_display = df_display.drop(columns=["url"])

                    # add index
                    df_display.insert(0, "ID", range(1, len(df_display) + 1))
                    # Convert DataFrame to markdown table
                    markdown_str += (
                        tabulate(
                            df_display,
                            headers="keys",
                            tablefmt="pipe",
                            showindex=False,
                        )
                        + "\n\n"
                    )
    return markdown_str


def convert_to_markdown_format_apify(hardware_search_results):
    markdown_str = "# HARDWARE RECOMMENDATION\n\n"

    for i, item in enumerate(hardware_search_results):
        markdown_str += f"## {i+1}. {item['component_name']}\n\n"
        markdown_str += f"**Search Query** : \n `{item['search_query']}`\n\n"
        markdown_str += f"**Specification** : \n {item['specification']}\n\n"
        markdown_str += (
            f"**List of Device Name Refer** :  {item['list_device_name_refer_llm']}\n\n"
        )
        search_results = item["result_search"]
        markdown_str += f"**List items from Amazon** :  \n\n"
        if search_results:
            df = pd.DataFrame(search_results)
            # Select and rename columns
            if not df.empty:
                columns_to_keep = [
                    "asin",
                    "url",
                    "title",
                    "price",
                    "stars",
                    "reviewsCount",
                ]
                columns_to_display = [
                    col for col in columns_to_keep if col in df.columns
                ]

                if columns_to_display:
                    df_display = df[columns_to_display].copy()
                    df_display["stars"] = pd.to_numeric(
                        df_display["stars"], errors="coerce"
                    )

                    # Extract the 'value' from each dictionary
                    df_display["price"] = df_display["price"].apply(
                        lambda x: x.get("value") if isinstance(x, dict) else None
                    )

                    # Rename columns for display
                    df_display = df_display.sort_values(by="stars", ascending=False)
                    df_display = df_display.head(10)
                    column_rename = {
                        "asin": "Product ID",
                        "title": "Product Description",
                        "price": "Price (SGD)",
                        "stars": "Rating",
                        "reviewsCount": "Reviews",
                    }
                    # add ID column
                    df_display.rename(
                        columns={
                            col: column_rename.get(col, col)
                            for col in columns_to_display
                        },
                        inplace=True,
                    )
                    df_display["Product ID"] = df_display.apply(
                        lambda row: f"[{row['Product ID']}]({row['url']})",
                        axis=1,
                    )

                    # replace ~ by - in column Product Description
                    df_display["Product Description"] = df_display[
                        "Product Description"
                    ].str.replace("~", "-")
                    # remove url column
                    df_display = df_display.drop(columns=["url"])

                    # add index
                    df_display.insert(0, "ID", range(1, len(df_display) + 1))
                    # Convert DataFrame to markdown table
                    markdown_str = "# HARDWARE RECOMMENDATION\n\n"
                    markdown_str += (
                        tabulate(
                            df_display,
                            headers="keys",
                            tablefmt="pipe",
                            showindex=False,
                        )
                        + "\n\n"
                    )
    return markdown_str
