from langgraph.graph import END, START, StateGraph

from tasks.report_planner.configuration import Configuration
from tasks.report_planner.states import (
    ReportState,
    ReportStateInput,
    ReportStateOutput,
    SectionOutputState,
    SectionState,
)

from .ecommerce_search import search_hardware_from_ecommerce
from .generate_hardware_queries import generate_hardware_queries
from .generate_plan import generate_report_plan, human_feedback
from .generate_queries import generate_queries
from .search_web import search_web
from .states import (
    Feedback,
    HardWareQueries,
    HardwareQuery,
    Queries,
    ReportState,
    ReportStateInput,
    ReportStateOutput,
    ScopeSpecifications,
    SearchQuery,
    Section,
    SectionOutputState,
    Sections,
    SectionState,
)
from .summarize_context import summarize_relevant_content_from_source
from .write_report import (
    gather_completed_sections,
    initiate_final_section_writing,
    rewrite_chapter_sections,
    write_final_report_academic_style,
    write_section,
)


def end_node(state: ReportState):
    """Final node"""
    return {
        "hardware_recommendation_search": state["hardware_recommendation_search"],
        "messages": "Procurement report completed. Thank you.",
    }


# Report section sub-graph --

# Add nodes
section_builder = StateGraph(SectionState, output=SectionOutputState)
section_builder.add_node("generate_queries", generate_queries)
section_builder.add_node("search_web", search_web)
section_builder.add_node(
    "summarize_content",
    summarize_relevant_content_from_source,
)
section_builder.add_node("write_section", write_section)

# Add edges
section_builder.add_edge(START, "generate_queries")
section_builder.add_edge("generate_queries", "search_web")
section_builder.add_edge("search_web", "summarize_content")
section_builder.add_edge("summarize_content", "write_section")

# Outer graph --

# Add nodes
report_planner_builder = StateGraph(
    ReportState,
    input=ReportStateInput,
    output=ReportStateOutput,
    config_schema=Configuration,
)
report_planner_builder.add_node("generate_report_plan", generate_report_plan)
report_planner_builder.add_node("human_feedback", human_feedback)
report_planner_builder.add_node(
    "build_section_with_web_research", section_builder.compile()
)
report_planner_builder.add_node("gather_completed_sections", gather_completed_sections)
report_planner_builder.add_node("rewrite_chapter_sections", rewrite_chapter_sections)
report_planner_builder.add_node(
    "write_final_report_academic_style", write_final_report_academic_style
)
report_planner_builder.add_node("generate_hardware_queries", generate_hardware_queries)

report_planner_builder.add_node(
    "search_hardware_from_ecommerce", search_hardware_from_ecommerce
)
report_planner_builder.add_node("end_node", end_node)


# Add edges
report_planner_builder.add_edge(START, "generate_report_plan")
report_planner_builder.add_edge("generate_report_plan", "human_feedback")
report_planner_builder.add_edge(
    "build_section_with_web_research", "gather_completed_sections"
)
report_planner_builder.add_conditional_edges(
    "gather_completed_sections",
    initiate_final_section_writing,
    ["rewrite_chapter_sections"],
)
report_planner_builder.add_edge(
    "rewrite_chapter_sections", "write_final_report_academic_style"
)
report_planner_builder.add_edge(
    "write_final_report_academic_style", "generate_hardware_queries"
)
report_planner_builder.add_edge(
    "generate_hardware_queries", "search_hardware_from_ecommerce"
)

report_planner_builder.add_edge("search_hardware_from_ecommerce", "end_node")
# report_planner_graph = builder.compile()
