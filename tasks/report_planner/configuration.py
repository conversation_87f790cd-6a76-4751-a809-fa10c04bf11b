import os
from dataclasses import dataclass, fields
from enum import Enum
from typing import Any, Dict, Optional

from langchain_core.runnables import RunnableConfig

DEFAULT_REPORT_STRUCTURE = """
1/ System Overview
2/ Hardware Components (categorized by type)
3/ Software Components
4/ User Interface Elements
…
8/ Safety Features
7/ Additional Considerations
…
9/ Conclusion
"""


class SearchAPI(Enum):
    PERPLEXITY = "perplexity"
    TAVILY = "tavily"
    ARXIV = "arxiv"


class PlannerProvider(Enum):
    ANTHROPIC = "anthropic"
    OPENAI = "openai"
    GROQ = "groq"


class WriterProvider(Enum):
    ANTHROPIC = "anthropic"
    OPENAI = "openai"
    GROQ = "groq"


class SummarizationProvider(Enum):
    ANTHROPIC = "anthropic"
    OPENAI = "openai"
    GROQ = "groq"


@dataclass(kw_only=True)
class Configuration:
    """The configurable fields for the chatbot."""

    report_structure: str = (
        DEFAULT_REPORT_STRUCTURE  # Defaults to the default report structure
    )
    number_of_queries: int = 5  # Number of search queries to generate per iteration
    max_search_depth: int = 2  # Maximum number of reflection + search iterations
    number_sources_per_query: int = 3  # Number of sources to include per search query
    max_tokens_per_source: int = 100000  # Maximum number of tokens per source
    planner_provider: PlannerProvider = (
        PlannerProvider.OPENAI
    )  # Defaults to Anthropic as provider
    planner_model: str = "o3-mini"  # Defaults to claude-3-7-sonnet-latest

    writer_provider: WriterProvider = (
        WriterProvider.OPENAI
    )  # Defaults to Anthropic as provider
    writer_model: str = "gpt-4o"  # Defaults to claude-3-5-sonnet-latest

    summarization_provider: SummarizationProvider = SummarizationProvider.GROQ
    summarization_model: str = "qwen-qwq-32b"

    search_api: SearchAPI = SearchAPI.ARXIV  # Default to TAVILY
    search_api_config: Optional[Dict[str, Any]] = None

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )
        values: dict[str, Any] = {
            f.name: os.environ.get(f.name.upper(), configurable.get(f.name))
            for f in fields(cls)
            if f.init
        }
        return cls(**{k: v for k, v in values.items() if v})
