import asyncio
from typing import Any, Dict, List, Literal, Optional

import aiohttp
from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.constants import Send
from langgraph.graph import END, START, StateGraph
from langgraph.types import Command, interrupt

from tasks.report_planner.configuration import Configuration
from tasks.report_planner.prompts import query_writer_instructions
from tasks.report_planner.states import Queries, SectionState
from tasks.report_planner.utils import get_config_value


def generate_queries(state: SectionState, config: RunnableConfig):
    """Generate search queries for a report section"""

    # Get state
    scope_specifications = state["scope_specifications"]
    section = state["section"]

    # Get configuration
    configurable = Configuration.from_runnable_config(config)
    number_of_queries = configurable.number_of_queries

    # Generate queries
    writer_provider = get_config_value(configurable.writer_provider)
    writer_model_name = get_config_value(configurable.writer_model)
    writer_model = init_chat_model(
        model=writer_model_name, model_provider=writer_provider, temperature=0
    )
    structured_llm = writer_model.with_structured_output(Queries)

    # Format system instructions
    system_instructions = query_writer_instructions.format(
        scope=scope_specifications.scope,
        section_topic=section.description,
        number_of_queries=number_of_queries,
        specifications=scope_specifications.specifications,
    )

    # Generate queries
    queries = structured_llm.invoke(
        [
            SystemMessage(content=system_instructions),
            HumanMessage(content="Generate search queries on the provided topic."),
        ]
    )

    return {"search_queries": queries.queries}
