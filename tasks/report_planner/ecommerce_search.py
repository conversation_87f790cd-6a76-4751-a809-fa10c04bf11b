import asyncio
import os
from typing import Any, Dict, List, Optional

import aiohttp
from apify_client import ApifyClientAsync
from langchain.chat_models import init_chat_model
from langchain_core.runnables import RunnableConfig

from tasks.report_planner.configuration import Configuration
from tasks.report_planner.states import ReportState
from tasks.report_planner.utils import (
    convert_to_markdown_format,
    convert_to_markdown_format_apify,
)


async def fetch_ecommerce_search_apify(
    session: aiohttp.ClientSession,
    query: str,
    domain: str = "SG",
    page: int = 1,
    max_retries: int = 3,
    retry_delay: float = 2.0,
) -> Optional[Dict[str, Any]]:
    """
    Fetch Amazon search results using Apify

    Args:
        session: aiohttp client session
        query: search term
        domain: Amazon domain (e.g., "sg" for Singapore)
        page: page number
        max_retries: maximum number of retry attempts
        retry_delay: delay between retries in seconds

    Returns:
        JSON response or None if all retries failed
    """
    apify_api_key = os.getenv("APIFY_API_KEY")
    apify_client = ApifyClientAsync(apify_api_key)
    apify_actor_id = "BG3WDrGdteHgZgbPK"

    # Prepare the Actor input
    run_input = {
        "categoryOrProductUrls": [{"url": f"https://www.amazon.com/s?k={query}"}],
        "maxItemsPerStartUrl": 2,
        "proxyCountry": domain,
        "maxOffers": 10,
        "scrapeSellers": False,
        "ensureLoadedProductDescriptionFields": False,
        "useCaptchaSolver": False,
        "scrapeProductVariantPrices": False,
        "scrapeProductDetails": True,
        "locationDeliverableRoutes": [
            "PRODUCT",
            "SEARCH",
            "OFFERS",
        ],
    }

    # Start an Actor and wait for it to finish.
    actor_client = apify_client.actor(apify_actor_id)
    call_result = await actor_client.call(run_input=run_input)

    if call_result is None:
        print("Actor run failed.")
        return None

    # Fetch results from the Actor run's default dataset.
    dataset_client = apify_client.dataset(call_result["defaultDatasetId"])
    list_items_result = await dataset_client.list_items(
        fields=["asin", "url", "title", "price", "stars", "reviewsCount"]
    )
    return list_items_result.items


async def fetch_ecommerce_search(
    session: aiohttp.ClientSession,
    query: str,
    domain: str = "sg",
    page: int = 1,
    max_retries: int = 3,
    retry_delay: float = 2.0,
) -> Optional[Dict[str, Any]]:
    """
    Fetch Amazon search results with retry logic

    Args:
        session: aiohttp client session
        query: search term
        domain: Amazon domain (e.g., "sg" for Singapore)
        page: page number
        api_key: Piloterr API key
        max_retries: maximum number of retry attempts
        retry_delay: delay between retries in seconds

    Returns:
        JSON response or None if all retries failed
    """
    pilloter_api_key = os.getenv("PILOTERR_API_KEY")
    url = "https://piloterr.com/api/v2/amazon/search"
    headers = {"x-api-key": pilloter_api_key}
    params = {"query": query, "domain": domain, "page": page}

    for attempt in range(max_retries):
        try:
            async with session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:  # Too Many Requests
                    print(
                        f"Rate limit exceeded. Retry attempt {attempt+1}/{max_retries}"
                    )
                    await asyncio.sleep(
                        retry_delay * (attempt + 1)
                    )  # Exponential backoff
                else:
                    print(
                        f"Error: HTTP {response.status}. Retry attempt {attempt+1}/{max_retries}"
                    )
                    await asyncio.sleep(retry_delay)
        except Exception as e:
            print(f"Request failed: {e}. Retry attempt {attempt+1}/{max_retries}")
            await asyncio.sleep(retry_delay)

    print(f"All retries failed for query: {query}")
    return None


async def process_ecommerce_searches(
    hardware_lst: List[Dict[str, Any]],
    rate_limit: int = 5,  # 5 requests per minute
    rate_period: int = 60,  # in seconds
) -> List[Dict[str, Any]]:
    """
    Process multiple Amazon search requests with rate limiting

    Args:
        hardware_lst: List of hardware search queries
        api_key: Piloterr API key
        rate_limit: maximum number of requests per rate_period
        rate_period: time period for rate limit in seconds

    Returns:
        List of response dictionaries
    """
    results = []

    # Calculate time between requests to maintain rate limit
    delay_between_requests = rate_period / rate_limit

    async with aiohttp.ClientSession() as session:
        for i, query_params in enumerate(hardware_lst):
            # If not the first request, delay to maintain rate limit
            if i > 0 and i % rate_limit == 0:
                print(
                    f"Rate limit reached. Waiting for {delay_between_requests} seconds..."
                )
                await asyncio.sleep(delay_between_requests)

            # Extract parameters
            query = query_params.search_query
            print(f"Processing request {i+1}/{len(hardware_lst)}: {query}")

            result_search = await fetch_ecommerce_search_apify(session, query)

            results.append(
                {
                    "component_name": query_params.component_name,
                    "search_query": query,
                    "specification": query_params.specification,
                    "list_device_name_refer_llm": query_params.list_device_name_refer,
                    "result_search": result_search,
                }
            )
            # Add a small delay between requests
            await asyncio.sleep(delay_between_requests)

    return results


def search_hardware_from_ecommerce(state: ReportState, config: RunnableConfig):
    """Search Amazon for hardware components based on the generated recommendations"""

    # Get hardware recommendations
    hardware_queries = state["hardware_queries"][:2]

    # Process Amazon search requests
    hard_search = asyncio.run(process_ecommerce_searches(hardware_queries))

    hard_search_markdown = convert_to_markdown_format_apify(hard_search)
    return {"hardware_recommendation_search": hard_search_markdown}
