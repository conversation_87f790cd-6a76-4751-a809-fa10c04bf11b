from typing import List, Literal

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.graph import END, START, StateGraph
from langgraph.types import Command
from pydantic import BaseModel, Field

from states import AgentState, ComparisonOutput

specifications_comparison_prompt = """
Given the scope
<scope>
{scope}
<scope>

and the original specifications
<specifications>
{specifications}
<specifications>

and the necessary specifications found through research
<research_specifications>
{research_specifications}
<research_specifications>

compare the two and list down missing specifications and under-specified ones.

Generate a list of {number_of_questions} questions to ask the user to refine the specifications.
"""


def specifications_comparison(state: AgentState):
    scope_specifications = state["scope_specifications"]
    scope = scope_specifications.scope
    specifications = scope_specifications.specifications
    research_specifications = state["reflexion_response"]

    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    structured_llm = llm.with_structured_output(ComparisonOutput)
    enhancer_instructions = specifications_comparison_prompt.format(
        scope=scope,
        specifications=specifications,
        research_specifications=research_specifications,
        number_of_questions=7,
    )

    planner_message = """Generate a list of questions to ask the user to refine the specifications based on the research done."""

    response = structured_llm.invoke(
        [
            SystemMessage(content=enhancer_instructions),
            HumanMessage(content=planner_message),
        ]
    )
    return {"specification_refinement_questionss": response}
