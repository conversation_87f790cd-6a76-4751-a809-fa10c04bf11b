from typing import Literal

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field

from states import AgentState, ScopeSpecifications


def route_research(state: AgentState) -> Literal["report_planner", "END"]:
    if state.current_rating >= 7:
        return "report_planner"
    else:
        return "END"  # TODO: To loop back to do more research to refine the SOR
