from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage

from states import AgentState, Sections

DEFAULT_REPORT_STRUCTURE = """
1- System Overview
2- Hardware Components (categorized by type)
3- Software Components
4- User Interface Elements
…
8- Safety Features
7- Additional Considerations
…
9- Conclusion
"""

REPORT_PLANNER_PROMPT = """I want a plan for a report that is concise and focused.

<Report Topic>
{scope}
</Report Topic>

<Specifications for project>
{specifications}
</Specifications for project>

<Task>
Generate a list of sections for the report. Your plan should be tight and focused with NO overlapping sections or unnecessary filler. 
The plan should comprehensively cover all aspects from the specifications

For example, a good report structure might look like:

<report organization>
{report_organization}
</report organization>

Each section should have the fields:

- Name - Name for this section of the report.
- Description - Brief overview of the main topics covered in this section.
- Research - Whether to perform web research for this section of the report. ( shoule be Yes for technical sections)
- Content - The content of the section, which you will leave blank for now.

Integration guidelines:
- Include examples and implementation details within main topic sections, not as separate sections
- Ensure each section has a distinct purpose with no content overlap
- Combine related concepts rather than separating them

Before submitting, review your structure to ensure it has no redundant sections and follows a logical flow.
</Task>

<Feedback>
Here is feedback on the report structure from review (if any):
{feedback}
</Feedback>
"""

# Structure Guideline
DEFAULT_REPORT_STRUCTURE = """
Given the scope and specifications,

Scope:
{scope}

Specifications:
{specifications}

The report structure should focus on breaking-down the user-provided topic
and building a comprehensive report in markdown using the following format:


1. Introduction (no web search needed)
    - Brief overview of the topic area


2. Main Body Sections:
    - Each section should focus on a sub-topic of the user-provided topic
    - Include any key concepts and definitions
    - Provide real-world examples or case studies where applicable


3. Conclusion (no web search needed)
    - Aim for 1 structural element (either a list of table) that distills the main body sections
    - Provide a concise summary of the report


When generating the final response in markdown, if there are special characters in the text,
such as the dollar symbol, ensure they are escaped properly for correct rendering e.g $25.5 should become \$25.5
"""


def report_planner(state: AgentState) -> AgentState:
    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    structured_llm = llm.with_structured_output(Sections)

    scope_specifications = state["scope_specifications"]
    scope = scope_specifications.scope
    specifications = scope_specifications.specifications
    topic = state["topic"]

    # Report planner instructions
    planner_message = """Generate the sections of the report. Your response must include a 'sections' field containing a list of sections. 
                        Each section must have: name, description, plan, research, and content fields."""

    report_planner_instructions = REPORT_PLANNER_PROMPT.format(
        scope=scope,
        specifications=specifications,
        report_organization=DEFAULT_REPORT_STRUCTURE,
        feedback="",
    )
    planner_message = """Generate the topics of the report. Your response must include a 'topics' field containing a list of topics. 
                        Each section must have: scope and specifications."""

    report_sections = structured_llm.invoke(
        [
            SystemMessage(content=report_planner_instructions),
            HumanMessage(content=planner_message),
        ]
    )

    # Get sections
    sections = report_sections.sections

    return {"sections": sections}
