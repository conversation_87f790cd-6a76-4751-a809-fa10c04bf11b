from typing import List

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field

from states import AgentState, ScopeSpecifications


class ReplanSpecificationsOutput(BaseModel):
    specifications: List[str] = Field(
        description="Specifications required for the project.",
    )


def replan_specifications(state: AgentState) -> AgentState:
    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    structured_llm = llm.with_structured_output(ReplanSpecificationsOutput)

    topic = state["topic"]
    questions = state["specification_refinement_questionss"]
    feedback = state["refinement_feedback"]
    original_specifications = state["scope_specifications"]
    revised_specifications = state["reflexion_response"]

    replan_specifications_instructions = """
    Given the questions, feedback from the user, together with the original and new specifications.
    <questions>
    {questions}
    </questions>

    <feedback>
    {feedback}
    </feedback>

    <original_specifications>
    {original_specifications}
    </original_specifications>

    <revised_specifications>
    {revised_specifications}
    </revised_specifications>

    Please generate a revised specifications for the project.
    Be objective and always list down pointers with starting words like the below:

    "The system shall not have..."
    "The application will have..."
    "The system must..."
    "The system must not..."

    List down pointers that we need the system or application to be.
    """

    system_topic_extraction_instructions = replan_specifications_instructions.format(
        questions=questions,
        feedback=feedback,
        original_specifications=original_specifications,
        revised_specifications=revised_specifications,
    )
    planner_message = """Please generate a revised specifications for the project. Do not stray too far off the original specifications, keep it technical."""

    new_scope_specifications = structured_llm.invoke(
        [
            SystemMessage(content=system_topic_extraction_instructions),
            HumanMessage(content=planner_message),
        ]
    )

    print(
        "#####################################################################################"
    )
    print(new_scope_specifications)
    print(
        "#####################################################################################"
    )

    new_scope = ScopeSpecifications(
        scope=topic, specifications=new_scope_specifications.specifications
    )

    return {"scope_specifications": new_scope}
