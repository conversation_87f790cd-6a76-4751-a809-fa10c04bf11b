from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage

from prompts import SPECIFICATIONS_ENHANCER_PROMPT
from states import AgentState, SpecificationsEnhancerOutput


def specifications_enhancer(state: AgentState):
    scope = state["scope_specifications"].scope
    specifications = state["scope_specifications"].specifications

    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    structured_llm = llm.with_structured_output(SpecificationsEnhancerOutput)

    enhancer_instructions = SPECIFICATIONS_ENHANCER_PROMPT.format(
        scope=scope, specifications=specifications
    )

    planner_message = (
        """Evaluate the scope and specifications of the project if its clear enough."""
    )
    response: SpecificationsEnhancerOutput = structured_llm.invoke(
        [
            SystemMessage(content=enhancer_instructions),
            HumanMessage(content=planner_message),
        ]
    )

    return {"specifications_enhancer_output": response}
