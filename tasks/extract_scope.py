from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field

from states import AgentState, AgentStateInput, ScopeSpecifications

# async def extract_scope_specifications(state: AgentState):
#     llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")

#     # Add JSON format instruction to prompt
#     topic_extraction_instructions = """
#     <context>
#     {context}
#     </context>

#     Return a valid JSON response in this exact format:
#     {{
#         "topic": [
#             {{
#                 "scope": "high-level overview text",
#                 "specifications": ["requirement 1", "requirement 2"]
#             }}
#         ]
#     }}
#     """

#     messages = [
#         SystemMessage(content=topic_extraction_instructions.format(context=state["topic"])),
#         HumanMessage(content="Generate the topics with scope and specifications in JSON format."),
#     ]

#     scope_specifications = llm.invoke(messages)

#     # Stream tokens
#     # full_content = ""
#     # async for chunk in llm.astream(messages):
#     #     if chunk.content:
#     #         full_content += chunk.content
#     #         # print(chunk.content, end="", flush=True)
#     #         # This yields tokens one by one
#     #         yield {"streaming_content": chunk.content, "node": "extract_scope"}

#     # # Parse the JSON response
#     # import json
#     # try:
#     #     parsed_data = json.loads(full_content)
#     #     scope_specifications = ScopeSpecifications(**parsed_data)
#     # except Exception as e:
#     #     # Fallback
#     #     yield {"error": f"Failed to parse JSON: {e}", "raw_content": full_content}
#     #     return  # Exit early on error

#     return {"scope_specifications": scope_specifications}


async def extract_scope_specifications(state: AgentStateInput) -> AgentState:
    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    structured_llm = llm.with_structured_output(ScopeSpecifications)

    topic = state.topic
    # Prompt to extract scope and specifications for a project
    topic_extraction_instructions = """ 

    <context>
    {context}
    </context>

    The scope should be a high-level overview of the main topics covered in the report, while the specifications contain requirements for the project with bullet points format.
    """

    system_topic_extraction_instructions = topic_extraction_instructions.format(
        context=topic
    )
    planner_message = """Generate the topics of the report. Your response must include a 'topics' field containing a list of topics. 
                        Each section must have: scope and specifications."""

    scope_specifications: ScopeSpecifications = await structured_llm.ainvoke(
        [
            SystemMessage(content=system_topic_extraction_instructions),
            HumanMessage(content=planner_message),
        ]
    )
    print("++" * 100)
    print(scope_specifications)
    print("++" * 100)
    return {"scope_specifications": scope_specifications}
