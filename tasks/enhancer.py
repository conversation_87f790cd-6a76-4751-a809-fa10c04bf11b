import json
from typing import List, Literal

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_tavily import <PERSON>lySearch
from langgraph.graph import END, START, StateGraph
from langgraph.prebuilt import create_react_agent
from langgraph.types import Command
from pydantic import BaseModel, Field

from states import AgentState, EnhancerOutput

# TODO:
# Add addiitonal step or node to search the web for similar projects thats related to the scope and specifications
# Acting as a experienced consultant and expert in the field of {scope}
# Learn from the projects and their top requirements
# Using the knowledge, ask questions to make this scope and specifications more detailed and robust
# For example, a detailed description of a delivery robot can be found here: https://en.wikipedia.org/wiki/Delivery_robot, what are
# the different types, what are some key considerations when it comes to building such a delivery robot?
# Use this knowledge to ask questions to the user to enhance the scope and specifications of current project as the user might have missed out important details

ENHANCER_PROMPT = """
Based on the scope and specificationsbelow.

<scope>
{scope}
<scope>

<specifications>
{specifications}
<specifications>

Your objective is to generate a list of questions helping the user uncover and clarify the requirements of the project.
Bear in mind that the user might not be an expert in the field, and the user might not know the right questions to ask.
These questions will heavily influence the direction of the project, as the end goal is to procure for the components needed to build the project.

For example, if the user wants a robot, the user might not know the different types of robots available, and the user might not know the different types of tasks that a robot can perform.

You are a experienced Consultant with expertise in transforming vague specifications into well-defined specifications. Your responsibilities include:
1. Understanding the original scope to identify key intent and requirements
2. Search the web to understand similar projects and requirements
3. Using knowledge from similar projects, ask relevant questions to the user that will directly impact the procurement of the components needed for the project
4. Using knowledge from similar projects, expand underdeveloped aspects of the specifications with reasonable assumptions and research from the web
Important: 
- Never assume anything.
- Answers are not mandatory, because users might not be able to answer the questions nor have the answers to the questions.
- Every web search should be properly cited, and the citation should be included in the reference field.

Your response should be in the following format:
<objective> - What is your objective here, what are you trying to achieve?

<question> - Question to ask the user
<rationale> - Reason for the question asked, how will it enhance the scope, what led to the question, and how it will help to clarify procurement of the components needed for the project
<reference> - Should only be url link, web cititations. Leave it empty if no reference is needed
<answer> - Answer that the user will provide, leave it empty if the user did not provide an answer

Example:
<question> - Could you provide more details on the office environment where the robot will be deployed? Specifically, what is the flooring type (e.g., carpet, tile, concrete), are there any inclines or ramps, and what is the typical width of hallways and doorways it will need to navigate?
<rationale> - Understanding the environment is crucial for selecting appropriate motors, wheels/tracks, and navigation parameters. Different flooring types offer varying levels of friction, inclines require more powerful motors, and hallway/doorway widths dictate the robot's maximum physical dimensions. This information will directly influence the selection of mechanical and electrical components.
<reference> - https://en.wikipedia.org/wiki/Robotics#Types_of_robots
<answer> - Flooring is mat.

"""


def enhancer(state: AgentState):
    scope_specifications = state["scope_specifications"]
    scope = scope_specifications.scope
    specifications = scope_specifications.specifications
    topic = state["topic"]

    # Initialize Tavily Search Tool
    tavily_search_tool = TavilySearch(
        max_results=5,
        topic="general",
        search_depth="basic",
    )

    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    enhancer_instructions = ENHANCER_PROMPT.format(
        scope=scope, specifications=specifications
    )

    # print(enhancer_instructions)
    agent = create_react_agent(
        model=llm,
        tools=[tavily_search_tool],
        prompt=enhancer_instructions,
        response_format=EnhancerOutput,
    )
    response = agent.invoke(
        {
            "messages": [
                HumanMessage(
                    content="Generate a list of questions to ask the user to enhance the scope and specifications of the project. Your response must include at least 3 questions."
                )
            ]
        }
    )
    structured_response = response["structured_response"]

    list_of_questions = []
    for q_model in structured_response.questions:
        list_of_questions.append(
            {
                "question": q_model.question,
                "rationale": q_model.rationale,
                "reference": q_model.reference,
                "answer": q_model.answer,
            }
        )

    print(list_of_questions)

    return Command(update={"enhancer_questions": list_of_questions}, goto="human_node")

    # print(response.objective)
    # print(response.questions)
    # for step in agent.stream(
    #     {"messages": [HumanMessage(content="Generate a list  of questions to ask the user to enhance the scope and specifications of the project. Your response must include at least 3 questions.")]},
    #     stream_mode="values",
    # ):
    #     step["messages"][-1].pretty_print()
