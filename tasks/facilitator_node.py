from typing import Literal

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.types import Command
from pydantic import BaseModel, Field

from prompts import FACILITATOR_PROMPT
from states import AgentState, ScopeSpecifications


class FacilitatorOutput(BaseModel):
    next: Literal["enhancer", "report_planner"] = Field(
        description="Determines which specialist to activate next in the workflow sequence: "
        "'enhancer' when scope of requirements requires clarification, expansion, or refinement"
        "'report_planner' when the report needs to be planned, research, or writing is required"
    )
    reason: str = Field(
        description="Detailed justification for the routing decision, explaining the rationale behind selecting the particular specialist and how this advances the task toward completion."
    )


def facilitator(
    state: AgentState,
) -> Command[Literal["specifications_enhancer", "report_planner"]]:
    scope_specifications = state["scope_specifications"]
    scope = scope_specifications.scope
    specifications = scope_specifications.specifications
    topic = state["topic"]

    llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    structured_llm = llm.with_structured_output(FacilitatorOutput)
    coordinator_instructions = FACILITATOR_PROMPT.format(
        scope=scope, specifications=specifications, default_response="Not relevant."
    )
    response = structured_llm.invoke(
        [
            SystemMessage(content=coordinator_instructions),
            HumanMessage(content=topic),
        ]
    )
    goto = response.next
    reason = response.reason

    return Command(goto=goto)
