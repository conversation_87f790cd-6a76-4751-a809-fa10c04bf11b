"""
Example usage of the Deep Research Agent API
"""

import asyncio
import json

import httpx


async def example_non_streaming():
    """Example of using the non-streaming research endpoint"""
    async with httpx.AsyncClient() as client:
        # Example research request
        request_data = {
            "topic": """
            DEVELOPMENT OF A CUSTOMIZED ROUTE DELIVERY ROBOT
            
            SPECIFICATIONS:
            - Fully automated delivery robot for office environment
            - Autonomous navigation using VSLAM
            - Mobile app control
            - 24 hour runtime on 8 hour charge
            - 10-inch operating screen
            - Max footprint: 38 x 38 x 103cm
            - 1-year warranty required
            """,
            "thread_id": "example-thread-1"
        }
        
        print("🚀 Starting research workflow...")
        response = await client.post(
            "http://localhost:8000/research",
            json=request_data,
            timeout=300.0  # 5 minute timeout
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Research completed: {result}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")


async def example_streaming():
    """Example of using the streaming research endpoint"""
    async with httpx.AsyncClient() as client:
        request_data = {
            "topic": """
            DEVELOPMENT OF A CUSTOMIZED ROUTE DELIVERY ROBOT
            
            SPECIFICATIONS:
            - Fully automated delivery robot for office environment
            - Autonomous navigation using VSLAM 
            - Mobile app control
            """,
            "thread_id": "streaming-example-1"
        }
        
        print("🚀 Starting streaming research workflow...")
        print("=" * 50)
        
        async with client.stream(
            "POST",
            "http://localhost:8000/research/stream",
            json=request_data,
            timeout=300.0
        ) as response:
            if response.status_code == 200:
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            event_data = json.loads(line[6:])  # Remove "data: " prefix
                            event_type = event_data.get("type")
                            
                            if event_type == "content":
                                print(event_data.get("content", ""), end="", flush=True)
                            elif event_type == "agent_start":
                                agent = event_data.get("data", {}).get("agent", "unknown")
                                print(f"\n🤖 [{agent}] starting...\n")
                            elif event_type == "tool_start":
                                tool = event_data.get("data", {}).get("tool", "unknown")
                                print(f"\n🔧 Using tool: {tool}")
                            elif event_type == "search_results":
                                urls = event_data.get("data", {}).get("urls", [])
                                print(f"\n🔍 Found {len(urls)} search results")
                                for i, url in enumerate(urls[:3], 1):  # Show first 3 URLs
                                    print(f"  {i}. {url}")
                            elif event_type == "completed":
                                print(f"\n✅ Research workflow completed!")
                            elif event_type == "error":
                                error = event_data.get("data", {}).get("error", "Unknown error")
                                print(f"\n❌ Error: {error}")
                                
                        except json.JSONDecodeError:
                            continue
            else:
                print(f"❌ Error: {response.status_code}")


async def example_get_history():
    """Example of getting conversation history"""
    async with httpx.AsyncClient() as client:
        thread_id = "example-thread-1"
        
        response = await client.get(f"http://localhost:8000/threads/{thread_id}/history")
        
        if response.status_code == 200:
            history = response.json()
            print(f"📜 Thread {thread_id} history:")
            print(f"Topic: {history.get('topic', 'N/A')}")
            print(f"Messages: {len(history.get('messages', []))}")
        else:
            print(f"❌ Error getting history: {response.status_code}")


async def main():
    """Run examples"""
    print("Deep Research Agent API Examples")
    print("=" * 40)
    
    # Test health endpoint
    async with httpx.AsyncClient() as client:
        response = await client.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("✅ API is healthy")
        else:
            print("❌ API is not responding")
            return
    
    print("\nChoose an example:")
    print("1. Non-streaming research")
    print("2. Streaming research")
    print("3. Get conversation history")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        await example_non_streaming()
    elif choice == "2":
        await example_streaming()
    elif choice == "3":
        await example_get_history()
    else:
        print("Invalid choice")


if __name__ == "__main__":
    asyncio.run(main())