import getpass
import operator
import os
from typing import Annotated, List, Literal, Optional, Sequence, TypedDict

from dotenv import load_dotenv
from langchain.chat_models import init_chat_model
from langchain.schema import BaseMessage
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_google_genai import Cha<PERSON><PERSON><PERSON>gleGenerativeAI
from langchain_tavily import <PERSON>lySearch
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from langgraph.managed.base import ManagedValue
from langgraph.prebuilt import create_react_agent
from pydantic import BaseModel, Field
from typing_extensions import TypedDict


# TODO: To remove this, there should be a neater way to handle and use supervisor agent
# Find a way to keep track of messages and use supervisor_agent
# Please read up:
# https://langchain-ai.lang.chat/langgraph/tutorials/multi_agent/agent_supervisor/#create-multi-agent-graph
# https://medium.com/@anuragmishra_27746/building-multi-agents-supervisor-system-from-scratch-with-langgraph-langsmith-b602e8c2c95d
class IsLastStepManager(ManagedValue[bool]):
    def __call__(self) -> bool:
        return self.loop.step == self.loop.stop - 1


IsLastStep = Annotated[bool, IsLastStepManager]


class RemainingStepsManager(ManagedValue[int]):
    def __call__(self) -> int:
        return self.loop.stop - self.loop.step


RemainingSteps = Annotated[int, RemainingStepsManager]


class QuestionModel(BaseModel):
    question: str
    rationale: str
    reference: str
    answer: str


class EnhancerOutput(BaseModel):
    objective: str
    questions: List[QuestionModel]


class SpecificationsEnhancerOutput(BaseModel):
    is_specifications_clear_enough: bool
    reason: str


class ScopeSpecifications(BaseModel):
    scope: str = Field(
        description="Name for this scope / topic of the report.",
    )
    specifications: str = Field(
        description="Specifications for the project.",
    )


class AgentState(TypedDict):
    topic: str  # Report topic
    scope_specifications: ScopeSpecifications
    specification_refinement_questionss: Optional[List[QuestionModel]] = []
    refinement_feedback: Optional[str] = None
    specifications_enhancer_output: Optional[SpecificationsEnhancerOutput] = None

    # From Supervisor Langgraph
    messages: Annotated[Sequence[BaseMessage], add_messages]
    is_last_step: IsLastStep
    remaining_steps: RemainingSteps

    # Facilitator
    enhancement_times: int = 0

    # Report planner
    hardware_recommendation_search: str


class ComparisonOutput(BaseModel):
    questions: Optional[List[QuestionModel]]


class AgentStateInput(BaseModel):
    topic: str  # Report topic
    messages: Annotated[Sequence[BaseMessage], add_messages]
