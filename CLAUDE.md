# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a deep research agent system built with LangGraph that automatically enhances project specifications and generates procurement reports through web research. The system uses a multi-agent architecture with a supervisor coordinating between specification enhancement and report planning agents.

## Development Setup

### Environment Setup
```bash
# Create virtual environment
python -m venv .venv
source ./.venv/bin/activate  # Linux/Mac
.venv\Scripts\Activate.ps1   # Windows

# Install dependencies
poetry install
# OR
pip install -r requirements.txt

# Install pre-commit hooks
pre-commit install
```

### Running the Application
```bash
# Start LangGraph server locally (opens in browser)
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.11 langgraph dev --allow-blocking

# Run directly with Python
python main.py

# Start FastAPI server
uvicorn api:app --host 0.0.0.0 --port 8000 --reload

# Run API examples
python api_examples.py
```

### Code Quality Commands
```bash
# Format code
black .
isort .

# Lint code
flake8

# Run pre-commit on all files
pre-commit run --all-files
```

### Model Generation
```bash
# Generate models from API contracts (run when contracts change)
datamodel-codegen --input api-contracts/user.yaml --output src/models/user.py --input-file-type openapi
```

## Architecture

### Core Components
- **Supervisor Agent**: Coordinates workflow between specification enhancer and report planner
- **Specifications Enhancer**: Improves and clarifies project specifications through multi-step refinement
- **Report Planner**: Conducts web research and generates procurement reports
- **Search Engines**: Multiple search providers (Tavily, ArXiv, Perplexity)

### Key Files
- `main.py`: Entry point and workflow orchestration
- `states.py`: Shared state definitions and data models
- `tasks/specifications_enhancer/`: Specification enhancement sub-graph
- `tasks/report_planner/`: Report generation sub-graph with web research
- `search_engine/`: Web search integrations

### LangGraph Configuration
- Main graph defined in `langgraph.json`
- Memory checkpointing enabled via MemorySaver
- Asynchronous execution with event streaming
- Human-in-the-loop feedback supported

## Required Environment Variables
- `GOOGLE_API_KEY`: For Gemini models
- `OPENAI_API_KEY`: For OpenAI models  
- `TAVILY_API_KEY`: For web search
- `PERPLEXITY_API_KEY`: For Perplexity search (optional)

## Dependencies
- LangGraph: Multi-agent workflow orchestration
- LangChain: LLM framework and integrations
- Multiple LLM providers: Google Gemini, OpenAI, Anthropic
- Search providers: Tavily, ArXiv, Perplexity
- FastAPI: Web API framework with async support
- Code quality: Black, isort, flake8, pre-commit

## API Endpoints
- `GET /`: Root endpoint
- `GET /health`: Health check
- `POST /research`: Start research workflow (non-streaming)
- `POST /research/stream`: Start research workflow with streaming
- `GET /threads/{thread_id}/history`: Get conversation history

## Common Issues
If encountering "blocking call" warnings, run with `--allow-blocking` flag or use async/await patterns for I/O operations.